# MpaasCrypto Client Hook脚本使用说明

## 功能概述

这个脚本专门用于Hook `com.alipay.mobile.common.mpaas_crypto.Client` 类的所有方法，并检测native方法使用的SO文件。

## Hook的方法

### Java方法
1. **构造函数** - 监控Client对象创建
2. **init()** - 初始化方法，监控初始化参数
3. **encode()** - 加密方法，支持多种加密类型
4. **decode()** - 解密方法，支持多种解密类型
5. **encryptSm4()** - SM4加密
6. **decryptSm4()** - SM4解密
7. **error()** - 错误信息获取
8. **exit()** - 退出清理

### Native方法检测
脚本会自动检测以下SO文件中的native方法：
- `libmpaas_crypto.so`
- `libopenssl.so`
- `libantssm.so`

## 加密类型常量

脚本会自动解析加密类型：
- `0` - CT_rsa_aes (RSA + AES)
- `1` - CT_ecc_aes (ECC + AES)
- `2` - CT_sm2_sm4 (SM2 + SM4)
- `3` - CT_sm2_sm4_zz (SM2 + SM4 自助)
- `4` - CT_sm2_sm4_rmk (SM2 + SM4 RMK)

## 使用方法

### 1. 基本使用
```bash
frida -U -l MpaasCrypto_Client_Hook脚本.js com.czcb.mbank
```

### 2. 检查SO文件状态
在Frida控制台中执行：
```javascript
checkMpaasCryptoSO();
```

### 3. 重新Hook Native方法
```javascript
hookMpaasCryptoNative();
```

## 输出格式示例

### 初始化方法
```
[MpaasCrypto] ================================================================================
[MpaasCrypto] 🎯 Class: Client
[MpaasCrypto] 🔧 Method: init (Native)
[MpaasCrypto] ⏰ 时间: 2024-01-15 10:30:45
[MpaasCrypto] 📥 Arguments:
[0] 类型: String
[0] 值: /data/data/com.czcb.mbank/cert/
[1] 类型: String
[1] 值: client.p12
[2] 类型: String
[2] 值: password123
[3] 类型: String
[3] 值: server.crt
[MpaasCrypto] 📤 Return value:
值: true
类型: Boolean
[MpaasCrypto] 📊 初始化后handle值: ********
[MpaasCrypto] ================================================================================
```

### 加密方法
```
[MpaasCrypto] ================================================================================
[MpaasCrypto] 🎯 Class: Client
[MpaasCrypto] 🔧 Method: encode (Native)
[MpaasCrypto] 📥 Arguments:
[0] 类型: Array
[0] 字节数组 (256 字节):
7b 22 74 72 61 6e 73 43 6f 64 65 22 3a 22 4d 42
30 30 30 30 31 30 22 2c 22 6c 6f 67 6f 6e 4e 61
[0] 字符串内容: {"transCode":"MB000010","logonName":"test",...}
[1] 类型: Array
[1] 字节数组 (32 字节):
d6 90 e9 ff cc e1 3d b7 16 b6 14 c2 28 fb 2c 05
[2] 类型: Number
[2] 值: 2
[MpaasCrypto] 📤 Return value:
二维字节数组:
  [0] (384 字节):
    十六进制: 48 38 07 28 9f 3c 4a 1b 7e 2d 8f 9a 3b 5c 6e 8d
    字符串: H8.(.<J.~-..;\n.
  [1] (16 字节):
    十六进制: a1 b2 c3 d4 e5 f6 07 18 29 3a 4b 5c 6d 7e 8f 90
    字符串: .........):[m~..
[MpaasCrypto] 🔐 加密类型: CT_sm2_sm4
[MpaasCrypto] ================================================================================
```

### SO文件检测
```
[MpaasCrypto] 🔍 检查SO文件加载情况:
[MpaasCrypto] ✅ libopenssl.so - 已加载，基址: 0x7f8a2c000000
[MpaasCrypto]   🔐 找到加密相关函数 (15 个):
[MpaasCrypto]     - SM4_encrypt @ 0x7f8a2c012340
[MpaasCrypto]     - SM4_decrypt @ 0x7f8a2c012380
[MpaasCrypto]     - SM4_set_key @ 0x7f8a2c0123c0
[MpaasCrypto] ✅ libmpaas_crypto.so - 已加载，基址: 0x7f8a2d000000
[MpaasCrypto]   🔐 找到加密相关函数 (8 个):
[MpaasCrypto]     - Java_com_alipay_mobile_common_mpaas_crypto_Client_encode @ 0x7f8a2d001234
[MpaasCrypto]     - Java_com_alipay_mobile_common_mpaas_crypto_Client_decode @ 0x7f8a2d001278
[MpaasCrypto] ✅ libantssm.so - 已加载，基址: 0x7f8a2e000000
```

### Native方法调用
```
[MpaasCrypto] 🔥 Native方法调用: encode (来自 libmpaas_crypto.so)
[MpaasCrypto]   参数数量: 5
[MpaasCrypto]   args[0]: 0x7f8a2f123456  // JNIEnv
[MpaasCrypto]   args[1]: 0x7f8a2f123478  // jobject
[MpaasCrypto]   args[2]: 0x7f8a2f12349a  // jbyteArray
[MpaasCrypto]   args[3]: 0x7f8a2f1234bc  // jbyteArray
[MpaasCrypto]   args[4]: 0x2              // jint (CT_sm2_sm4)
[MpaasCrypto] 🔥 Native方法返回: encode -> 0x7f8a2f1234de
```

## 分析价值

### 1. 加密算法分析
- 了解支持的加密算法类型
- 分析加密参数和密钥
- 监控加密过程的输入输出

### 2. 证书和密钥管理
- 监控证书文件路径
- 分析密钥加载过程
- 了解证书验证机制

### 3. 数据流分析
- 跟踪数据加密的完整流程
- 分析加密前后的数据变化
- 了解数据传输的安全机制

### 4. SO文件定位
- 确定native方法的实际实现位置
- 分析SO文件的加载顺序
- 为进一步的native层分析提供基础

## 实际使用场景

### 场景1: 分析登录加密
```bash
# 1. 启动Hook脚本
frida -U -l MpaasCrypto_Client_Hook脚本.js com.czcb.mbank

# 2. 在APP中进行登录操作
# 3. 观察输出，重点关注：
#    - init方法的证书路径和密码
#    - encode方法的加密类型和数据
```

### 场景2: 分析不同加密类型
```bash
# 观察不同业务场景使用的加密类型：
# - 登录: 可能使用CT_sm2_sm4
# - 转账: 可能使用CT_sm2_sm4_rmk
# - 查询: 可能使用CT_rsa_aes
```

### 场景3: SO文件分析
```javascript
// 在Frida控制台中检查SO状态
checkMpaasCryptoSO();

// 如果发现新的SO文件，重新Hook
hookMpaasCryptoNative();
```

## 故障排除

### 1. 类未找到
```
[MpaasCrypto ERROR] Hook Client类失败: ClassNotFoundException
```
**解决方案**: 确认APP是否使用了mpaas_crypto组件

### 2. SO文件未加载
```
[MpaasCrypto] ❌ libmpaas_crypto.so - 未加载
```
**解决方案**: 
- 等待APP完全启动
- 触发相关功能让SO文件加载
- 检查APP是否包含该SO文件

### 3. Native方法Hook失败
**解决方案**:
- 检查符号名称是否正确
- 确认SO文件已加载
- 尝试不同的符号命名规则

## 扩展功能

### 1. 数据保存
可以修改脚本保存加密数据：
```javascript
// 保存加密前后的数据对比
var encryptionLog = {
    timestamp: new Date().toISOString(),
    input: bytesToHex(bArr1),
    output: bytesToHex(result),
    cryptoType: cryptoType
};
```

### 2. 实时分析
```javascript
// 分析加密模式和频率
if (cryptoType === 2) { // SM2+SM4
    analyzeSM2SM4Pattern(bArr1, result);
}
```

### 3. 性能监控
```javascript
// 监控加密操作的性能
var startTime = Date.now();
var result = this.encode(bArr1, bArr2, cryptoType);
var endTime = Date.now();
log("加密耗时: " + (endTime - startTime) + "ms");
```

## 注意事项

1. **性能影响**: Hook会在每次加密操作时触发，可能影响性能
2. **数据安全**: 输出的数据可能包含敏感信息，注意保护
3. **兼容性**: 脚本采用纯监控模式，不修改任何返回值
4. **权限要求**: 需要Root权限和适当的Frida配置

这个Hook脚本可以帮助您深入了解稠州银行APP的加密机制，特别是mpaas_crypto组件的工作原理。
