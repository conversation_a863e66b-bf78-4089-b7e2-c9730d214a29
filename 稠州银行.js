
function bytesToString (arr) {
    var str = '';
    var i;
    arr = new Uint8Array(arr);
    for (i in arr) {
        str += String.fromCharCode(arr[i]);
    }
    return str;
}

function bytes2String2 (jbytes) {
    var string = Java.use('java.lang.String');
    return string.$new(jbytes);
}

function stringToByte (str) {
    var bytes = new Array();
    var len, c;
    len = str.length;
    for (var i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if (c >= 0x010000 && c <= 0x10FFFF) {
            bytes.push(((c >> 18) & 0x07) | 0xF0);
            bytes.push(((c >> 12) & 0x3F) | 0x80);
            bytes.push(((c >> 6) & 0x3F) | 0x80);
            bytes.push((c & 0x3F) | 0x80);
        } else if (c >= 0x000800 && c <= 0x00FFFF) {
            bytes.push(((c >> 12) & 0x0F) | 0xE0);
            bytes.push(((c >> 6) & 0x3F) | 0x80);
            bytes.push((c & 0x3F) | 0x80);
        } else if (c >= 0x000080 && c <= 0x0007FF) {
            bytes.push(((c >> 6) & 0x1F) | 0xC0);
            bytes.push((c & 0x3F) | 0x80);
        } else {
            bytes.push(c & 0xFF);
        }
    }
    return bytes;
}

function bytesToHex (arr) {
    var str = '';
    var k, j;
    for (var i = 0; i < arr.length; i++) {
        k = arr[i];
        j = k;
        if (k < 0) {
            j = k + 256;
        }
        if (j < 16) {
            str += "0";
        }
        str += j.toString(16);
    }
    return str;
}

function dumphex (var1) {
    hexdump((var1), {
        length: 256,
        header: true,
        ansi: true
    })
}

function hook_java2 () {
    Java.perform(function () {
        var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");
        ClientRpcPack.encrypt.implementation = function (var1) {
            console.log("Request:");
            console.log(bytes2String2(var1));
            var retval = this.encrypt(var1);
            console.log(bytesToHex(retval))
            return retval;
        }
        ClientRpcPack.decrypt.implementation = function (var1) {
            console.log("Response:");
            console.log(bytesToHex(var1))
            var retval = this.decrypt(var1);
            console.log(bytes2String2(retval));
            return retval;
        }
    });
}

function hook_java3 () {
    Java.perform(function () {
        var RpcInvoker = Java.use("com.alipay.mobile.common.rpc.RpcInvoker");
        RpcInvoker.a.overload('java.lang.String', '[B', 'java.lang.String', 'com.alipay.mobile.common.rpc.transport.InnerRpcInvokeContext', '[I').implementation = function (var1, var2, var3, var4, var5) {
            console.log("Sign:", bytes2String2(var2));
            var str_var1 = bytes2String2(var2);
            // console.log("signtype:", var5)
            var retval = this.a(var1, var2, var3, var4, var5);
            // console.log(retval)
            return retval;
        }
        let RpcSignUtil = Java.use("com.alipay.mobile.common.transport.utils.RpcSignUtil");
        RpcSignUtil["a"].overload('int').implementation = function (signType) {
            console.log('a is called' + ', ' + 'signType: ' + signType);
            let ret = this.a(signType);
            console.log('a ret value is ' + ret);
            return ret;
        };
        // let SignData = Java.use("com.alipay.mobile.common.transport.utils.RpcSignUtil$SignData");
        // SignData["createSignDataBySignResult"].implementation = function (signResult) {
        //     console.log('createSignDataBySignResult is called' + ', ' + 'signResult: ' + signResult);
        //     let ret = this.createSignDataBySignResult(signResult);
        //     console.log('createSignDataBySignResult ret value is ' + ret);
        //     return ret;
        // };

    });
}

function hook_java1 () {
    Java.perform(function () {
        var Utils = Java.use("com.czcb.mbank.base.utils.Utils");
        Utils.isRoot.implementation = function () {
            var retval = this.isRoot();
            console.log("Root Pass!")
            return false;
        }
    });
}

function hook_so1 () {
    var SM4 = Module.findExportByName("libopenssl.so", "SM4_encrypt");
    console.log("------------------------------------------->")
    console.log(SM4);
    if (SM4 != null) {
        Interceptor.attach(SM4, {
            onEnter: function (args) {
                console.log("----------------------------------------------------------")
                console.log(hexdump(args[0]));
                console.log(hexdump(args[1]));
                console.log(hexdump(args[2]));
            },
            onLeave: function (retval) {
                // console.log("retval", hexdump(ptr(retval)));
                console.log("----------------------------------------------------------")
            }
        });
    }
}



function hook_java4 () {
    Java.perform(function () {
        var MiscUtils = Java.use("com.alipay.mobile.common.transport.utils.MiscUtils");
        MiscUtils.generateRandomStr.implementation = function (var1) {
            var retval = this.generateRandomStr(var1);
            console.log("---------  key  ------------->");
            console.log(retval);
            return retval;
        }
    });
}

function hook_java5 () {
    Java.perform(function () {
        var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");
        ClientRpcPack.b.overload().implementation = function () {
            var retval = this.b();
            console.log("-----------    SM2 PublicKey   ----------->");
            console.log(retval);
            return retval;
        }
    });
}


function main () {
    // hook_java1();
    // hook_java2();
    // hook_java3();
    // hook_java4();
    // hook_java5();
    hook_so1();
}

setImmediate(main)