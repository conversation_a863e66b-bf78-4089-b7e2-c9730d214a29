# SO Hook脚本使用示例

## 快速开始

### 1. 使用通用脚本监控您提到的偏移

```bash
# 运行通用SO Hook脚本
frida -U -l 通用SO_Hook脚本.js com.czcb.mbank
```

脚本会自动监控以下偏移：
- `0x212EC0` (您提到的关键偏移)
- `0x212EC4` 
- `0x212EC8`

### 2. 使用稠州银行专用配置

```bash
# 运行专用配置脚本
frida -U -l 稠州银行SO监控配置.js com.czcb.mbank
```

## 输出格式对比

### 您提供的格式：
```
.rodata:0000000000212EC0 DCB 0xD6, 0x90, 0xE9, 0xFF, 0xCC, 0xE1, 0x3D, 0xB7, 0x16
.rodata:0000000000212EC0 byte_212EC0     DCB 0xD6, 0x90, 0xE9, 0xFF, 0xCC, 0xE1, 0x3D, 0xB7, 0x16
.rodata:0000000000212EC0                 ; DATA XREF: SM4_set_key+6C↑o
.rodata:0000000000212EC0                 ;             SM4_set_key+74↑o ...
```

### 脚本输出格式：
```
[CZCB-SO] ================================================================================
[CZCB-SO] 监控时间: 2024-01-15 10:30:45
[CZCB-SO] 内存地址: 0x7f8a2c212ec0
[CZCB-SO] 偏移地址: 0x212EC0
.rodata:00007F8A2C212EC0 DCB 0xD6, 0x90, 0xE9, 0xFF, 0xCC, 0xE1, 0x3D, 0xB7, 0x16
.rodata:00007F8A2C212ED0 DCB 0xB6, 0x14, 0xC2, 0x28, 0xFB, 0x2C, 0x05, 0x2B, 0x67
[CZCB-SO] ================================================================================
```

## 实际使用场景

### 场景1: 监控SM4加密过程

```bash
# 1. 启动脚本
frida -U -l 稠州银行SO监控配置.js com.czcb.mbank

# 2. 在APP中触发登录
# 3. 观察控制台输出，会看到：
```

```
[CZCB-SO] 🔐 SM4_set_key 被调用
[CZCB-SO] 读取偏移: 0x212EC0 -> 绝对地址: 0x7f8a2c212ec0
.rodata:00007F8A2C212EC0 DCB 0xD6, 0x90, 0xE9, 0xFF, 0xCC, 0xE1, 0x3D, 0xB7, 0x16
```

### 场景2: 动态读取特定偏移

在Frida控制台中执行：

```javascript
// 读取您关心的偏移
readCZCBOffset("0x212EC0");

// 读取其他偏移
readCZCBOffset("0x212EC4");
readCZCBOffset("0x212EC8");
```

### 场景3: 搜索内存模式

```javascript
// 搜索SM4相关的内存模式
searchCZCBPattern();
```

## 配置自定义偏移

### 方法1: 修改脚本配置

编辑 `稠州银行SO监控配置.js` 中的配置：

```javascript
var CZCB_CONFIG = {
    SO_NAME: "libopenssl.so",
    OFFSETS: [
        "0x212EC0",  // 您的目标偏移
        "0x212EC4",  // 添加更多偏移
        "0x你的偏移"  // 自定义偏移
    ],
    DATA_LENGTH: 256
};
```

### 方法2: 动态添加偏移

在Frida控制台中：

```javascript
// 更新配置
updateCZCBConfig({
    OFFSETS: ["0x212EC0", "0x新偏移1", "0x新偏移2"],
    DATA_LENGTH: 512
});
```

## 监控不同SO文件

### 监控其他SO文件

```javascript
// 修改配置监控不同的SO
updateCZCBConfig({
    SO_NAME: "libpassguard.so",
    OFFSETS: ["0x1000", "0x2000"]
});
```

### 同时监控多个SO

可以运行多个脚本实例：

```bash
# 终端1: 监控libopenssl.so
frida -U -l 通用SO_Hook脚本.js com.czcb.mbank

# 终端2: 监控libpassguard.so  
# (修改配置后)
frida -U -l 通用SO_Hook脚本.js com.czcb.mbank
```

## 高级用法

### 1. Hook特定函数并监控内存

脚本会自动Hook以下SM4函数：
- `SM4_encrypt`
- `SM4_decrypt`
- `SM4_set_key`
- `SM4_set_encrypt_key`
- `SM4_set_decrypt_key`

### 2. 内存模式搜索

脚本会自动搜索以下模式：
- `A3 B1 BA C6` (SM4 S-box)
- `56 AA 55 AA` (常见密钥模式)
- `01 23 45 67` (测试密钥)
- `FE DC BA 98` (测试模式)

### 3. 连续监控

脚本支持连续监控，每秒检查一次内存变化。

## 故障排除

### 1. SO未找到

```
[CZCB-SO ERROR] 未找到SO: libopenssl.so
```

**解决方案**:
- 检查SO文件名
- 查看脚本输出的已加载SO列表
- 确认APP已完全启动

### 2. 偏移读取失败

```
[CZCB-SO ERROR] 读取偏移 0x212EC0 失败
```

**解决方案**:
- 确认偏移地址正确
- 检查内存权限
- 尝试减少DATA_LENGTH

### 3. 权限问题

**解决方案**:
- 确保设备已Root
- 重启Frida Server
- 检查SELinux设置

## 输出数据分析

### 理解输出格式

```
.rodata:00007F8A2C212EC0 DCB 0xD6, 0x90, 0xE9, 0xFF
```

- `rodata`: 只读数据段
- `00007F8A2C212EC0`: 绝对内存地址
- `DCB`: 定义字节数据
- `0xD6, 0x90...`: 十六进制字节值

### 数据含义

根据您提供的信息，`0x212EC0` 偏移可能包含：
- SM4算法的S-box数据
- 加密密钥材料
- 算法常量

通过监控这些地址的变化，可以分析：
- 密钥设置过程
- 加密算法执行
- 数据变换过程

## 扩展功能

脚本支持进一步扩展：
- 添加更多SO文件监控
- 自定义数据解析
- 导出监控数据
- 实时数据对比

根据具体分析需求，可以修改脚本添加更多功能。
