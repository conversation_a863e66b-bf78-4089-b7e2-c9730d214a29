/**
 * 稠州银行APP 安全键盘专用监控脚本
 * 专门用于监控安全键盘，确保不影响键盘正常启动
 * 采用被动监控策略，只在键盘使用时才Hook
 */

// 工具函数
function formatOutput(className, methodName, args, retval) {
    console.log("🎯 Class: " + className);
    console.log("🔧 Method: " + methodName);
    console.log("📥 Arguments:");
    if (args && args.length > 0) {
        for (let i = 0; i < args.length; i++) {
            console.log("[" + i + "]：" + args[i]);
        }
    } else {
        console.log("无参数");
    }
    console.log("📤 Return value:");
    console.log(retval || "void");
    console.log("=" * 50);
}

// 1. 监控PassGuard安全键盘 (被动监控)
function monitorPassGuard() {
    // 等待用户触发登录界面后再Hook
    setTimeout(function() {
        Java.perform(function() {
            try {
                var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");
                
                // 监控构造函数
                PassGuardEdit.$init.overload('android.content.Context').implementation = function(context) {
                    console.log("🎯 PassGuardEdit 构造函数被调用");
                    console.log("📥 Context: " + context);
                    
                    // 调用原构造函数
                    var result = this.$init(context);
                    
                    console.log("✅ PassGuardEdit 初始化完成");
                    console.log("=" * 50);
                    return result;
                };
                
                // 监控初始化方法
                if (PassGuardEdit.init) {
                    PassGuardEdit.init.implementation = function() {
                        console.log("🎯 PassGuardEdit.init 被调用");
                        
                        // 调用原方法
                        var result = this.init.apply(this, arguments);
                        
                        console.log("✅ PassGuardEdit.init 完成");
                        formatOutput("PassGuardEdit", "init", Array.prototype.slice.call(arguments), result);
                        return result;
                    };
                }
                
                // 监控设置加密模式
                if (PassGuardEdit.setEncryptMode) {
                    PassGuardEdit.setEncryptMode.implementation = function() {
                        var result = this.setEncryptMode.apply(this, arguments);
                        formatOutput("PassGuardEdit", "setEncryptMode", Array.prototype.slice.call(arguments), result);
                        return result;
                    };
                }
                
                // 监控获取加密文本 (只在用户输入完成后触发)
                PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
                    var result = this.getSM2SM4Ciphertext();
                    
                    try {
                        console.log("🎯 Class: PassGuardEdit");
                        console.log("🔧 Method: getSM2SM4Ciphertext");
                        console.log("📥 Arguments: 无参数");
                        console.log("📤 Return value:");
                        console.log("🔐 Encrypted Password: " + result);
                        console.log("=" * 50);
                    } catch (e) {
                        // 静默处理
                    }
                    
                    return result;
                };
                
                // 监控密码复杂度检测
                PassGuardEdit.isSimple.implementation = function() {
                    var result = this.isSimple();
                    
                    try {
                        console.log("🎯 Class: PassGuardEdit");
                        console.log("🔧 Method: isSimple");
                        console.log("📥 Arguments: 无参数");
                        console.log("📤 Return value:");
                        console.log("🔍 Is Simple Password: " + result);
                        console.log("=" * 50);
                    } catch (e) {
                        // 静默处理
                    }
                    
                    return result;
                };
                
                // 监控获取MD5值
                if (PassGuardEdit.getMD5) {
                    PassGuardEdit.getMD5.implementation = function() {
                        var result = this.getMD5();
                        formatOutput("PassGuardEdit", "getMD5", [], result);
                        return result;
                    };
                }
                
                console.log("[+] PassGuardEdit 被动监控设置完成");
            } catch (e) {
                console.log("[-] PassGuardEdit 监控失败: " + e);
            }
        });
    }, 10000); // 延迟10秒，确保APP完全启动
}

// 2. 监控CFCA安全输入
function monitorCFCA() {
    setTimeout(function() {
        Java.perform(function() {
            try {
                var HKELocalSipEditText = Java.use("com.cfca.mobile.sipedit.hke.HKELocalSipEditText");
                
                // 监控获取加密数据
                HKELocalSipEditText.getEncryptData.implementation = function() {
                    var result = this.getEncryptData();
                    formatOutput("HKELocalSipEditText", "getEncryptData", [], result);
                    return result;
                };
                
                console.log("[+] CFCA HKELocalSipEditText 监控设置完成");
            } catch (e) {
                console.log("[-] CFCA 监控失败: " + e);
            }
        });
    }, 12000); // 延迟12秒
}

// 3. 监控自定义安全键盘
function monitorCustomKeyboard() {
    setTimeout(function() {
        Java.perform(function() {
            try {
                var KeyboardFullSafeWindow = Java.use("com.czcb.mbank.common.keyboard.KeyboardFullSafeWindow");
                
                // 监控PIN码处理
                if (KeyboardFullSafeWindow.getPin) {
                    KeyboardFullSafeWindow.getPin.implementation = function() {
                        var result = this.getPin.apply(this, arguments);
                        formatOutput("KeyboardFullSafeWindow", "getPin", Array.prototype.slice.call(arguments), result);
                        return result;
                    };
                }
                
                // 监控一次性填充
                if (KeyboardFullSafeWindow.oneTimePadding) {
                    KeyboardFullSafeWindow.oneTimePadding.implementation = function() {
                        var result = this.oneTimePadding.apply(this, arguments);
                        formatOutput("KeyboardFullSafeWindow", "oneTimePadding", Array.prototype.slice.call(arguments), result);
                        return result;
                    };
                }
                
                console.log("[+] 自定义安全键盘监控设置完成");
            } catch (e) {
                console.log("[-] 自定义安全键盘监控失败: " + e);
            }
        });
    }, 15000); // 延迟15秒
}

// 4. 监控键盘工具类
function monitorKeyboardUtils() {
    setTimeout(function() {
        Java.perform(function() {
            try {
                var KeyboardUtils = Java.use("com.czcb.mbank.common.keyboard.KeyboardUtils");
                
                // 监控PassGuard初始化
                if (KeyboardUtils.initPassGuard) {
                    KeyboardUtils.initPassGuard.implementation = function() {
                        console.log("🎯 KeyboardUtils.initPassGuard 被调用");
                        
                        var result = this.initPassGuard.apply(this, arguments);
                        
                        formatOutput("KeyboardUtils", "initPassGuard", Array.prototype.slice.call(arguments), result);
                        return result;
                    };
                }
                
                console.log("[+] KeyboardUtils 监控设置完成");
            } catch (e) {
                console.log("[-] KeyboardUtils 监控失败: " + e);
            }
        });
    }, 8000); // 延迟8秒
}

// 主函数
function main() {
    console.log("🔐 稠州银行APP 安全键盘专用监控脚本启动");
    console.log("📌 采用被动监控策略，不干扰键盘初始化");
    console.log("⏰ 分阶段延迟Hook，确保不影响正常流程");
    
    // 分阶段启动监控
    monitorKeyboardUtils();      // 8秒后
    monitorPassGuard();          // 10秒后
    monitorCFCA();              // 12秒后
    monitorCustomKeyboard();     // 15秒后
    
    console.log("✅ 安全键盘监控脚本设置完成");
    console.log("📊 监控组件:");
    console.log("   - KeyboardUtils (8秒后启动)");
    console.log("   - PassGuardEdit (10秒后启动)");
    console.log("   - CFCA安全输入 (12秒后启动)");
    console.log("   - 自定义安全键盘 (15秒后启动)");
    console.log("⚠️  所有监控均为被动模式，不修改任何值");
    console.log("=" * 60);
}

// 立即执行
setImmediate(main);
