/**
 * MpaasCrypto Client类专用Hook脚本
 * 监控com.alipay.mobile.common.mpaas_crypto.Client类的所有方法
 * 检测native方法使用的SO文件
 */

// 工具函数
function log(message) {
    console.log("[MpaasCrypto] " + message);
}

function logError(message) {
    console.log("[MpaasCrypto ERROR] " + message);
}

// 字节数组转十六进制
function bytesToHex(arr) {
    if (!arr) return "null";
    var str = '';
    var bytes = new Uint8Array(arr);
    for (var i = 0; i < Math.min(arr.length, 64); i++) {
        var hex = bytes[i].toString(16).padStart(2, '0');
        str += hex + " ";
        if ((i + 1) % 16 === 0) str += "\n";
    }
    if (arr.length > 64) str += "... (总长度: " + arr.length + " 字节)";
    return str;
}

// 字节数组转字符串
function bytesToString(arr) {
    if (!arr) return "null";
    try {
        var string = Java.use('java.lang.String');
        return string.$new(arr);
    } catch (e) {
        return "无法转换为字符串";
    }
}

// 格式化输出
function formatOutput(className, methodName, args, retval, isNative) {
    log("=" * 80);
    log("🎯 Class: " + className);
    log("🔧 Method: " + methodName + (isNative ? " (Native)" : ""));
    log("⏰ 时间: " + new Date().toLocaleString());
    
    log("📥 Arguments:");
    if (args && args.length > 0) {
        for (let i = 0; i < args.length; i++) {
            var arg = args[i];
            log("[" + i + "] 类型: " + (arg ? arg.constructor.name : "null"));
            
            if (arg && arg.constructor.name === "Array") {
                log("[" + i + "] 字节数组 (" + arg.length + " 字节):");
                log(bytesToHex(arg));
                log("[" + i + "] 字符串内容: " + bytesToString(arg));
            } else {
                log("[" + i + "] 值: " + arg);
            }
        }
    } else {
        log("无参数");
    }
    
    log("📤 Return value:");
    if (retval) {
        if (retval.constructor.name === "Array") {
            if (retval.length > 0 && retval[0] && retval[0].constructor.name === "Array") {
                // 二维数组 (encode方法返回)
                log("二维字节数组:");
                for (var i = 0; i < retval.length; i++) {
                    log("  [" + i + "] (" + retval[i].length + " 字节):");
                    log("    十六进制: " + bytesToHex(retval[i]));
                    log("    字符串: " + bytesToString(retval[i]));
                }
            } else {
                // 一维数组
                log("字节数组 (" + retval.length + " 字节):");
                log("十六进制: " + bytesToHex(retval));
                log("字符串: " + bytesToString(retval));
            }
        } else {
            log("值: " + retval);
            log("类型: " + retval.constructor.name);
        }
    } else {
        log("null/void");
    }
    
    log("=" * 80);
}

// 检测SO文件加载情况
function checkSOFiles() {
    log("🔍 检查SO文件加载情况:");
    
    var soFiles = [
        "libopenssl.so",
        "libmpaas_crypto.so", 
        "libantssm.so",
        "libssl.so",
        "libcrypto.so"
    ];
    
    soFiles.forEach(function(soName) {
        try {
            var baseAddr = Module.findBaseAddress(soName);
            if (baseAddr) {
                log("✅ " + soName + " - 已加载，基址: " + baseAddr);
                
                // 尝试查找相关的导出函数
                var exports = Module.enumerateExportsSync(soName);
                var cryptoFunctions = exports.filter(function(exp) {
                    return exp.name.toLowerCase().includes('sm4') || 
                           exp.name.toLowerCase().includes('sm2') ||
                           exp.name.toLowerCase().includes('aes') ||
                           exp.name.toLowerCase().includes('rsa') ||
                           exp.name.toLowerCase().includes('encrypt') ||
                           exp.name.toLowerCase().includes('decrypt');
                });
                
                if (cryptoFunctions.length > 0) {
                    log("  🔐 找到加密相关函数 (" + cryptoFunctions.length + " 个):");
                    cryptoFunctions.slice(0, 10).forEach(function(func) {
                        log("    - " + func.name + " @ " + func.address);
                    });
                    if (cryptoFunctions.length > 10) {
                        log("    - ... 还有 " + (cryptoFunctions.length - 10) + " 个函数");
                    }
                }
            } else {
                log("❌ " + soName + " - 未加载");
            }
        } catch (e) {
            log("❌ " + soName + " - 检查失败: " + e);
        }
    });
}

// Hook Native方法并尝试定位SO文件
function hookNativeMethods() {
    log("🎯 开始Hook Native方法...");
    
    // Native方法列表
    var nativeMethods = [
        "decode",
        "decryptSm4", 
        "encode",
        "encryptSm4",
        "error",
        "exit",
        "init"
    ];
    
    nativeMethods.forEach(function(methodName) {
        try {
            // 尝试通过符号查找native方法
            var possibleSymbols = [
                "Java_com_alipay_mobile_common_mpaas_1crypto_Client_" + methodName,
                "Java_com_alipay_mobile_common_mpaas_crypto_Client_" + methodName
            ];
            
            possibleSymbols.forEach(function(symbol) {
                var soFiles = ["libmpaas_crypto.so", "libopenssl.so", "libantssm.so"];
                soFiles.forEach(function(soName) {
                    try {
                        var addr = Module.findExportByName(soName, symbol);
                        if (addr) {
                            log("🎯 找到Native方法: " + methodName + " 在 " + soName + " @ " + addr);
                            
                            // Hook这个native方法
                            Interceptor.attach(addr, {
                                onEnter: function(args) {
                                    log("🔥 Native方法调用: " + methodName + " (来自 " + soName + ")");
                                    log("  参数数量: " + args.length);
                                    for (var i = 0; i < Math.min(args.length, 5); i++) {
                                        log("  args[" + i + "]: " + args[i]);
                                    }
                                },
                                onLeave: function(retval) {
                                    log("🔥 Native方法返回: " + methodName + " -> " + retval);
                                }
                            });
                        }
                    } catch (e) {
                        // 忽略查找失败
                    }
                });
            });
        } catch (e) {
            log("Hook Native方法失败: " + methodName + " - " + e);
        }
    });
}

// Hook Client类的所有方法
function hookClientClass() {
    Java.perform(function() {
        try {
            var Client = Java.use("com.alipay.mobile.common.mpaas_crypto.Client");
            log("✅ 找到Client类: " + Client);
            
            // Hook构造函数
            Client.$init.implementation = function() {
                log("🏗️ Client构造函数被调用");
                var result = this.$init();
                log("🏗️ Client构造完成，handle: " + this.handle.value);
                return result;
            };
            
            // Hook init方法
            Client.init.implementation = function(str1, str2, str3, str4) {
                var result = this.init(str1, str2, str3, str4);
                formatOutput("Client", "init", [str1, str2, str3, str4], result, true);
                log("📊 初始化后handle值: " + this.handle.value);
                return result;
            };
            
            // Hook encode方法
            Client.encode.implementation = function(bArr1, bArr2, cryptoType) {
                var result = this.encode(bArr1, bArr2, cryptoType);
                formatOutput("Client", "encode", [bArr1, bArr2, cryptoType], result, true);
                
                // 解析加密类型
                var cryptoTypeStr = "";
                switch(cryptoType) {
                    case 0: cryptoTypeStr = "CT_rsa_aes"; break;
                    case 1: cryptoTypeStr = "CT_ecc_aes"; break;
                    case 2: cryptoTypeStr = "CT_sm2_sm4"; break;
                    case 3: cryptoTypeStr = "CT_sm2_sm4_zz"; break;
                    case 4: cryptoTypeStr = "CT_sm2_sm4_rmk"; break;
                    default: cryptoTypeStr = "未知类型(" + cryptoType + ")";
                }
                log("🔐 加密类型: " + cryptoTypeStr);
                
                return result;
            };
            
            // Hook decode方法
            Client.decode.implementation = function(bArr1, bArr2, cryptoType) {
                var result = this.decode(bArr1, bArr2, cryptoType);
                formatOutput("Client", "decode", [bArr1, bArr2, cryptoType], result, true);
                
                // 解析加密类型
                var cryptoTypeStr = "";
                switch(cryptoType) {
                    case 0: cryptoTypeStr = "CT_rsa_aes"; break;
                    case 1: cryptoTypeStr = "CT_ecc_aes"; break;
                    case 2: cryptoTypeStr = "CT_sm2_sm4"; break;
                    case 3: cryptoTypeStr = "CT_sm2_sm4_zz"; break;
                    case 4: cryptoTypeStr = "CT_sm2_sm4_rmk"; break;
                    default: cryptoTypeStr = "未知类型(" + cryptoType + ")";
                }
                log("🔓 解密类型: " + cryptoTypeStr);
                
                return result;
            };
            
            // Hook encryptSm4方法
            Client.encryptSm4.implementation = function(bArr1, bArr2) {
                var result = this.encryptSm4(bArr1, bArr2);
                formatOutput("Client", "encryptSm4", [bArr1, bArr2], result, true);
                return result;
            };
            
            // Hook decryptSm4方法
            Client.decryptSm4.implementation = function(bArr1, bArr2) {
                var result = this.decryptSm4(bArr1, bArr2);
                formatOutput("Client", "decryptSm4", [bArr1, bArr2], result, true);
                return result;
            };
            
            // Hook error方法
            Client.error.implementation = function() {
                var result = this.error();
                formatOutput("Client", "error", [], result, true);
                return result;
            };
            
            // Hook exit方法
            Client.exit.implementation = function() {
                log("🚪 Client.exit被调用");
                var result = this.exit();
                log("🚪 Client.exit完成");
                return result;
            };
            
            log("✅ Client类所有方法Hook完成");
            
        } catch (e) {
            logError("Hook Client类失败: " + e);
        }
    });
}

// 主函数
function main() {
    log("🚀 MpaasCrypto Client Hook脚本启动");
    
    // 检查SO文件
    setTimeout(function() {
        checkSOFiles();
        hookNativeMethods();
    }, 2000);
    
    // Hook Java类
    hookClientClass();
    
    log("✅ Hook脚本初始化完成");
    log("📊 监控内容:");
    log("   - Client类构造函数");
    log("   - init方法 (初始化)");
    log("   - encode/decode方法 (加解密)");
    log("   - encryptSm4/decryptSm4方法 (SM4加解密)");
    log("   - error/exit方法 (错误处理)");
    log("   - Native方法调用追踪");
    log("=" * 80);
}

// 导出控制函数
global.checkMpaasCryptoSO = function() {
    checkSOFiles();
};

global.hookMpaasCryptoNative = function() {
    hookNativeMethods();
};

// 启动脚本
setImmediate(main);
