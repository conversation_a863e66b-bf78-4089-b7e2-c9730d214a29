# 通用SO Hook脚本使用说明

## 功能特性

- ✅ 监控指定SO文件的特定偏移地址
- ✅ 支持多种数据显示格式 (十六进制、ASCII、Hexdump)
- ✅ 连续监控内存变化
- ✅ Hook函数调用时监控内存
- ✅ 监控内存写入操作
- ✅ 搜索内存中的特定模式
- ✅ 动态配置调整

## 快速开始

### 1. 基本配置

编辑脚本中的CONFIG部分：

```javascript
var CONFIG = {
    SO_NAME: "libopenssl.so",           // 目标SO文件名
    OFFSETS: [                          // 要监控的偏移地址
        "0x212EC0",
        "0x212EC4", 
        "0x212EC8"
    ],
    DATA_LENGTH: 256,                   // 读取数据长度
    MONITOR_INTERVAL: 1000,             // 监控间隔(毫秒)
    CONTINUOUS_MONITOR: true,           // 是否连续监控
    OUTPUT_FORMAT: "both"               // 输出格式: "hex", "ascii", "both"
};
```

### 2. 运行脚本

```bash
frida -U -l 通用SO_Hook脚本.js com.czcb.mbank
```

## 使用场景

### 场景1: 监控SM4加密过程

```javascript
// 配置
var CONFIG = {
    SO_NAME: "libopenssl.so",
    OFFSETS: ["0x212EC0"],  // SM4密钥位置
    DATA_LENGTH: 16,        // SM4密钥长度
    OUTPUT_FORMAT: "hex"
};
```

### 场景2: 监控多个关键地址

```javascript
var CONFIG = {
    SO_NAME: "libpassguard.so",
    OFFSETS: [
        "0x1000",  // 密码缓冲区
        "0x2000",  // 加密密钥
        "0x3000"   // 随机数
    ],
    DATA_LENGTH: 64,
    CONTINUOUS_MONITOR: true
};
```

### 场景3: Hook函数调用监控

```javascript
// 在main函数中启用
hookFunctionAndMonitor("libopenssl.so", "SM4_encrypt", ["0x212EC0"]);
```

## 输出格式说明

### 十六进制格式
```
[SO-Hook] 十六进制数据:
0x48 0x89 0xe5 0x48 0x83 0xec 0x20 0x48
0x89 0x7d 0xf8 0x48 0x89 0x75 0xf0 0x48
```

### ASCII格式
```
[SO-Hook] ASCII数据:
H..H.. H.}.H.u.H
```

### Hexdump格式
```
[SO-Hook] Hexdump格式:
           0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F  0123456789ABCDEF
00000000  48 89 e5 48 83 ec 20 48 89 7d f8 48 89 75 f0 48  H..H.. H.}.H.u.H
00000010  89 55 e8 48 89 4d e0 4c 89 45 d8 4c 89 4d d0 48  .U.H.M.L.E.L.M.H
```

## 动态控制

脚本运行后，可以在Frida控制台中使用以下命令：

### 更新配置
```javascript
updateConfig({
    SO_NAME: "libnew.so",
    OFFSETS: ["0x5000"],
    DATA_LENGTH: 128
});
```

### 读取特定偏移
```javascript
readOffset("0x212EC0");
```

### 搜索内存模式
```javascript
searchPattern("48 89 e5");  // 搜索特定字节序列
```

## 高级功能

### 1. 内存写入监控

```javascript
// 在main函数中启用
hookMemoryWrite(CONFIG.SO_NAME, CONFIG.OFFSETS);
```

### 2. 内存模式搜索

```javascript
// 搜索特定的字节模式
searchMemoryPattern("libopenssl.so", "48 89 e5");
```

### 3. 函数Hook监控

```javascript
// Hook特定函数，在调用时监控内存
hookFunctionAndMonitor("libopenssl.so", "SM4_encrypt", ["0x212EC0"]);
```

## 实际使用示例

### 示例1: 监控稠州银行SM4加密

```bash
# 1. 修改配置
var CONFIG = {
    SO_NAME: "libopenssl.so",
    OFFSETS: ["0x212EC0"],
    DATA_LENGTH: 16,
    OUTPUT_FORMAT: "hex"
};

# 2. 运行脚本
frida -U -l 通用SO_Hook脚本.js com.czcb.mbank

# 3. 在APP中触发加密操作
# 4. 观察控制台输出
```

### 示例2: 动态调整监控

```javascript
// 在Frida控制台中执行
updateConfig({
    OFFSETS: ["0x212EC0", "0x212EC4", "0x212EC8"],
    DATA_LENGTH: 64,
    MONITOR_INTERVAL: 500
});
```

## 故障排除

### 1. SO未找到
```
[SO-Hook ERROR] 未找到SO: libopenssl.so
```
**解决方案**: 
- 检查SO文件名是否正确
- 查看脚本输出的可用SO列表
- 确认SO已加载

### 2. 内存读取失败
```
[SO-Hook ERROR] 读取偏移 0x212EC0 失败
```
**解决方案**:
- 检查偏移地址是否正确
- 确认内存区域可读
- 减少DATA_LENGTH

### 3. 权限不足
**解决方案**:
- 确保设备已Root
- 检查Frida Server权限
- 尝试重启Frida Server

## 配置参数详解

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| SO_NAME | String | 目标SO文件名 | "libopenssl.so" |
| OFFSETS | Array | 偏移地址列表 | ["0x1000", "0x2000"] |
| DATA_LENGTH | Number | 读取数据长度(字节) | 256 |
| MONITOR_INTERVAL | Number | 监控间隔(毫秒) | 1000 |
| CONTINUOUS_MONITOR | Boolean | 是否连续监控 | true |
| OUTPUT_FORMAT | String | 输出格式 | "hex", "ascii", "both" |

## 注意事项

1. **性能影响**: 连续监控会消耗CPU资源，建议适当调整间隔
2. **内存权限**: 某些内存区域可能无法读取
3. **SO加载时机**: 脚本会等待2秒确保SO已加载
4. **数据长度**: 过大的DATA_LENGTH可能导致读取失败

## 扩展功能

脚本支持以下扩展：
- 自定义数据解析函数
- 内存变化对比
- 数据导出功能
- 多SO同时监控

根据具体需求，可以修改脚本添加更多功能。
