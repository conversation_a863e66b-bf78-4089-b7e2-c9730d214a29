# 稠州银行手机银行APP安全分析报告

## 1. 概述

本报告对稠州银行手机银行APP进行了全面的安全分析，重点关注加解密算法、网络通信、安全键盘、OWASP漏洞等关键安全组件。

## 2. 加解密算法分析

### 2.1 SM4加密算法
**类名**: `com.czcb.mbank.base.utils.SM4Utils`
- **功能**: 国密SM4对称加密算法实现
- **关键方法**: 
  - `encrypt()` - 加密方法
  - `decrypt()` - 解密方法
- **安全特点**: 使用国产密码算法，符合国密标准

### 2.2 RSA加密算法
**类名**: `com.czcb.mbank.hce.security.RSACerPlus`
- **功能**: RSA非对称加密算法
- **用途**: 主要用于密钥交换和数字签名

### 2.3 AES加密算法
**类名**: `com.czcb.mbank.hce.security.AESCoder`
- **功能**: AES对称加密算法
- **用途**: 数据加密传输

### 2.4 MD5哈希算法
**类名**: `com.czcb.mbank.base.utils.MD5Util`
- **功能**: MD5哈希算法
- **安全风险**: MD5已被证明存在碰撞攻击风险，建议升级到SHA-256

### 2.5 CFCA安全组件
**类名**: `com.czcb.mbank.common.cfca.util.CFCAUtil`
- **功能**: 中国金融认证中心(CFCA)安全组件
- **用途**: 数字证书管理和PKI服务

## 3. 网络通信接口分析

### 3.1 RPC通信框架
**类名**: `com.czcb.mbank.base.net.RpcTaskHelper`
- **功能**: RPC任务处理框架
- **关键方法**: `performTask()` - 执行RPC任务

### 3.2 登录请求模型
**类名**: `com.czcb.mbank.login.rpc.LoginRpcRequestModel`
- **功能**: 登录相关的RPC请求构建
- **关键接口**:
  - `MB000010` - 用户登录接口
  - `ZX000003` - 获取用户信息接口
  - `ZX000012` - 手机号验证接口
  - `ZX000013` - 短信验证码验证接口

### 3.3 网络任务执行器
**类名**: `com.czcb.mbank.base.net.impl.RpcTaskWorkerImpl`
- **功能**: 网络任务的具体执行实现
- **安全特点**: 支持加密传输和签名验证

## 4. 安全键盘分析

### 4.1 PassGuard安全键盘
**类名**: `cn.passguard.PassGuardEdit`
- **功能**: 第三方安全键盘组件
- **安全特点**:
  - 防截屏
  - 防键盘记录
  - 密码强度检测
  - 支持SM2+SM4加密

### 4.2 CFCA安全输入
**类名**: `com.cfca.mobile.sipedit.HKELocalSipEditText`
- **功能**: CFCA提供的安全输入组件
- **安全特点**:
  - 硬件级安全
  - 支持PIN码加密
  - 防篡改保护

### 4.3 自定义安全键盘
**类名**: `com.czcb.mbank.common.keyboard.KeyboardFullSafeWindow`
- **功能**: 银行自研的安全键盘
- **安全特点**:
  - 多种键盘类型支持
  - 随机数字键盘
  - 密码复杂度检测
  - 支持多种加密模式(A0, A1, E0, E1, AE0, AE1)

## 5. OWASP安全漏洞分析

### 5.1 WebView安全配置
**类名**: `com.czcb.mbank.h5service.activity.WebViewActivity`
**潜在风险**:
- ✅ 已禁用文件URL访问: `setAllowFileAccessFromFileURLs(false)`
- ✅ 已禁用通用访问: `setAllowUniversalAccessFromFileURLs(false)`
- ⚠️ 启用了JavaScript: `setJavaScriptEnabled(true)`
- ⚠️ 混合内容模式设置为允许: `setMixedContentMode(0)`

### 5.2 数据存储安全
- ✅ 使用了安全的加密存储
- ✅ 敏感数据经过加密处理
- ✅ 使用了硬件安全模块(HSM)

### 5.3 网络通信安全
- ✅ 使用HTTPS协议
- ✅ 实现了证书绑定
- ✅ 支持双向SSL认证

### 5.4 输入验证
- ✅ 实现了输入长度限制
- ✅ 密码复杂度验证
- ✅ 防SQL注入保护

## 6. 关键安全组件总结

### 6.1 加密算法组件
1. **SM4Utils** - 国密SM4加密
2. **RSACerPlus** - RSA加密
3. **AESCoder** - AES加密
4. **MD5Util** - MD5哈希(建议升级)

### 6.2 网络通信组件
1. **RpcTaskHelper** - RPC任务处理
2. **LoginRpcRequestModel** - 登录请求构建
3. **RpcTaskWorkerImpl** - 网络任务执行

### 6.3 安全输入组件
1. **PassGuardEdit** - PassGuard安全键盘
2. **HKELocalSipEditText** - CFCA安全输入
3. **KeyboardFullSafeWindow** - 自定义安全键盘

## 7. 安全建议

### 7.1 高优先级
1. 将MD5哈希算法升级为SHA-256或更安全的算法
2. 加强WebView的安全配置，限制JavaScript权限
3. 实施更严格的证书绑定策略

### 7.2 中优先级
1. 定期更新第三方安全组件
2. 加强日志安全，避免敏感信息泄露
3. 实施运行时应用自我保护(RASP)

### 7.3 低优先级
1. 优化用户体验与安全的平衡
2. 加强安全意识培训
3. 定期进行安全审计

## 8. 结论

稠州银行手机银行APP在安全架构设计上较为完善，采用了多层次的安全防护措施：

**优势**:
- 使用了国密算法，符合监管要求
- 实现了多种安全键盘方案
- 网络通信采用了加密和签名验证
- 遵循了大部分OWASP安全最佳实践

**需要改进的地方**:
- MD5算法存在安全风险
- WebView配置可以进一步加强
- 部分安全配置需要优化

总体而言，该APP的安全防护水平较高，但仍有优化空间。
