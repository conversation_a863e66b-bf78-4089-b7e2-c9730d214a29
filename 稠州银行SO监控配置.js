/**
 * 稠州银行APP SO监控专用配置
 * 基于通用SO Hook脚本的预配置版本
 * 针对您提到的偏移地址进行监控
 */

// 稠州银行专用配置
var CZCB_CONFIG = {
    // 目标SO文件
    SO_NAME: "libopenssl.so",
    
    // 根据您提供的信息配置的偏移地址
    OFFSETS: [
        "0x212EC0",  // 您提到的关键偏移
        "0x212EC4",  // 相邻地址
        "0x212EC8",  // 相邻地址
        "0x212ECC",  // 相邻地址
        "0x212ED0"   // 相邻地址
    ],
    
    // 监控配置
    DATA_LENGTH: 256,           // 读取256字节数据
    MONITOR_INTERVAL: 1000,     // 每秒监控一次
    CONTINUOUS_MONITOR: true,   // 启用连续监控
    OUTPUT_FORMAT: "both"       // 同时显示十六进制和ASCII
};

// 工具函数
function log(message) {
    console.log("[CZCB-SO] " + message);
}

function logError(message) {
    console.log("[CZCB-SO ERROR] " + message);
}

// 格式化输出，类似您提供的格式
function formatCZCBOutput(address, buffer, length) {
    log("=" * 80);
    log("监控时间: " + new Date().toLocaleString());
    log("内存地址: " + address);
    log("偏移地址: 0x" + address.sub(Module.findBaseAddress(CZCB_CONFIG.SO_NAME)).toString(16).toUpperCase());
    
    // 按照您提供的格式输出
    var bytes = new Uint8Array(buffer);
    var output = "";
    
    for (var i = 0; i < Math.min(length, bytes.length); i += 16) {
        // 地址部分
        var addr = address.add(i);
        output += ".rodata:" + addr.toString(16).padStart(16, '0').toUpperCase();
        
        // 字节数据部分
        var hexPart = "";
        var asciiPart = "";
        
        for (var j = 0; j < 16 && (i + j) < bytes.length; j++) {
            var byte = bytes[i + j];
            hexPart += "0x" + byte.toString(16).padStart(2, '0').toUpperCase();
            if (j < 15 && (i + j + 1) < bytes.length) hexPart += ", ";
            
            // ASCII部分
            if (byte >= 32 && byte <= 126) {
                asciiPart += String.fromCharCode(byte);
            } else {
                asciiPart += ".";
            }
        }
        
        output += " DCB " + hexPart;
        if (asciiPart.trim().length > 0) {
            output += " ; " + asciiPart;
        }
        output += "\n";
    }
    
    console.log(output);
    log("=" * 80);
}

// 读取指定偏移的内存
function readCZCBMemory(baseAddress, offset, length) {
    try {
        var offsetInt = parseInt(offset, 16);
        var targetAddress = baseAddress.add(offsetInt);
        var buffer = Memory.readByteArray(targetAddress, length);
        
        log("读取偏移: " + offset + " -> 绝对地址: " + targetAddress);
        formatCZCBOutput(targetAddress, buffer, length);
        
        return true;
    } catch (e) {
        logError("读取偏移 " + offset + " 失败: " + e);
        return false;
    }
}

// 监控所有配置的偏移
function monitorAllOffsets() {
    var baseAddress = Module.findBaseAddress(CZCB_CONFIG.SO_NAME);
    if (!baseAddress) {
        logError("未找到SO: " + CZCB_CONFIG.SO_NAME);
        return;
    }
    
    log("开始监控 " + CZCB_CONFIG.SO_NAME + " (基址: " + baseAddress + ")");
    
    CZCB_CONFIG.OFFSETS.forEach(function(offset) {
        readCZCBMemory(baseAddress, offset, CZCB_CONFIG.DATA_LENGTH);
    });
}

// Hook SM4相关函数
function hookSM4Functions() {
    var sm4Functions = [
        "SM4_encrypt",
        "SM4_decrypt", 
        "SM4_set_key",
        "SM4_set_encrypt_key",
        "SM4_set_decrypt_key"
    ];
    
    sm4Functions.forEach(function(funcName) {
        try {
            var funcAddr = Module.findExportByName(CZCB_CONFIG.SO_NAME, funcName);
            if (funcAddr) {
                log("找到函数: " + funcName + " at " + funcAddr);
                
                Interceptor.attach(funcAddr, {
                    onEnter: function(args) {
                        log("🔐 " + funcName + " 被调用");
                        
                        // 在函数调用时监控关键偏移
                        setTimeout(function() {
                            monitorAllOffsets();
                        }, 100);
                    },
                    onLeave: function(retval) {
                        log("🔐 " + funcName + " 执行完成");
                    }
                });
            }
        } catch (e) {
            log("Hook " + funcName + " 失败: " + e);
        }
    });
}

// 搜索SM4相关的内存模式
function searchSM4Patterns() {
    var baseAddress = Module.findBaseAddress(CZCB_CONFIG.SO_NAME);
    if (!baseAddress) {
        logError("未找到SO: " + CZCB_CONFIG.SO_NAME);
        return;
    }
    
    // SM4常见的魔数和模式
    var patterns = [
        "A3 B1 BA C6",  // SM4 S-box开始
        "56 AA 55 AA",  // 常见的SM4密钥模式
        "01 23 45 67",  // 测试密钥模式
        "FE DC BA 98"   // 另一种测试模式
    ];
    
    patterns.forEach(function(pattern) {
        try {
            var moduleInfo = Process.getModuleByName(CZCB_CONFIG.SO_NAME);
            var results = Memory.scanSync(moduleInfo.base, moduleInfo.size, pattern);
            
            if (results.length > 0) {
                log("找到模式 '" + pattern + "' 的 " + results.length + " 个匹配:");
                results.forEach(function(result, index) {
                    var offset = result.address.sub(baseAddress);
                    log("  匹配 " + (index + 1) + ": 偏移=0x" + offset.toString(16).toUpperCase());
                    
                    // 读取匹配位置的更多数据
                    var buffer = Memory.readByteArray(result.address, 64);
                    formatCZCBOutput(result.address, buffer, 64);
                });
            }
        } catch (e) {
            log("搜索模式失败: " + e);
        }
    });
}

// 主函数
function main() {
    log("稠州银行SO监控脚本启动");
    log("目标SO: " + CZCB_CONFIG.SO_NAME);
    log("监控偏移: " + CZCB_CONFIG.OFFSETS.join(", "));
    
    // 等待SO加载
    setTimeout(function() {
        var baseAddress = Module.findBaseAddress(CZCB_CONFIG.SO_NAME);
        if (!baseAddress) {
            logError("SO未加载，等待中...");
            
            // 列出当前加载的SO
            log("当前已加载的SO文件:");
            Process.enumerateModules().forEach(function(module) {
                if (module.name.includes(".so")) {
                    console.log("  - " + module.name + " (基址: " + module.base + ")");
                }
            });
            return;
        }
        
        log("SO已加载: " + CZCB_CONFIG.SO_NAME + " (基址: " + baseAddress + ")");
        
        // 立即读取一次所有偏移
        log("执行初始内存读取...");
        monitorAllOffsets();
        
        // Hook SM4相关函数
        log("设置SM4函数Hook...");
        hookSM4Functions();
        
        // 搜索SM4相关模式
        log("搜索SM4内存模式...");
        searchSM4Patterns();
        
        // 启动连续监控
        if (CZCB_CONFIG.CONTINUOUS_MONITOR) {
            log("启动连续监控 (间隔: " + CZCB_CONFIG.MONITOR_INTERVAL + "ms)");
            setInterval(function() {
                log("定时监控检查...");
                monitorAllOffsets();
            }, CZCB_CONFIG.MONITOR_INTERVAL);
        }
        
    }, 3000); // 延迟3秒等待SO加载
    
    log("脚本初始化完成");
}

// 导出控制函数
global.readCZCBOffset = function(offset) {
    var baseAddress = Module.findBaseAddress(CZCB_CONFIG.SO_NAME);
    if (baseAddress) {
        readCZCBMemory(baseAddress, offset, CZCB_CONFIG.DATA_LENGTH);
    }
};

global.updateCZCBConfig = function(newConfig) {
    Object.assign(CZCB_CONFIG, newConfig);
    log("配置已更新");
};

global.searchCZCBPattern = function(pattern) {
    searchSM4Patterns();
};

// 启动脚本
setImmediate(main);
