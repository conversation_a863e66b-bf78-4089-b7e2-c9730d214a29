Java.perform(function () {
    // 目标类的完整路径（包名+类名）
    const TargetClass = Java.use('com.alipay.mobile.common.mpaas_crypto.Client');
    
    // 获取该类的所有方法名
    const methodNames = TargetClass.class.getDeclaredMethodNames();
    
    methodNames.forEach(methodName => {
      // 筛选出 native 方法（Frida 中 native 方法的 implementation 为 null）
      if (TargetClass[methodName].implementation === null) {
        console.log(`[+] 发现 native 方法：${methodName}`);
        
        // 对 native 方法进行 hook
        TargetClass[methodName].implementation = function () {
          console.log(`\n[*] 调用 native 方法：${methodName}`);
          
          // 获取当前执行的函数地址
          const funcAddr = Module.getCurrentFunction();
          if (funcAddr) {
            // 根据函数地址查找所属模块（.so 文件）
            const module = Module.findModuleByAddress(funcAddr);
            if (module) {
              console.log(`[+] 所属 .so 文件：${module.path}`);
            } else {
              console.log(`[-] 未找到所属模块`);
            }
          }
          
          // 执行原方法（注意：native 方法的参数需要按实际类型传递，这里用 arguments 透传）
          const result = this[methodName].apply(this, arguments);
          return result;
        };
      }
    });
  });