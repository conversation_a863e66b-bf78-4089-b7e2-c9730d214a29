/**
 * 稠州银行APP Hook测试脚本
 * 用于验证Hook是否正常工作
 */

// 测试Java环境
function testJavaEnvironment() {
    Java.perform(function() {
        console.log("✅ Java环境正常");
        
        // 测试基本Java类
        try {
            var String = Java.use("java.lang.String");
            console.log("✅ 可以访问java.lang.String");
        } catch (e) {
            console.log("❌ 无法访问java.lang.String: " + e);
        }
        
        // 测试应用包名
        try {
            var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
            var packageName = context.getPackageName();
            console.log("📱 当前应用包名: " + packageName);
        } catch (e) {
            console.log("❌ 无法获取包名: " + e);
        }
    });
}

// 测试关键类是否存在
function testKeyClasses() {
    Java.perform(function() {
        var testClasses = [
            "com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack",
            "com.alipay.mobile.common.transport.utils.MiscUtils",
            "com.alipay.mobile.common.rpc.RpcInvoker",
            "com.czcb.mbank.base.utils.Utils",
            "com.czcb.mbank.base.utils.SM4Utils",
            "com.czcb.mbank.base.utils.MD5Util",
            "cn.passguard.PassGuardEdit",
            "com.czcb.mbank.login.rpc.LoginRpcRequestModel"
        ];
        
        console.log("🔍 测试关键类是否存在:");
        testClasses.forEach(function(className) {
            try {
                var clazz = Java.use(className);
                console.log("✅ " + className + " - 存在");
            } catch (e) {
                console.log("❌ " + className + " - 不存在");
            }
        });
    });
}

// 测试Native库
function testNativeLibraries() {
    console.log("🔍 测试Native库:");
    
    var libraries = [
        "libopenssl.so",
        "libssl.so", 
        "libcrypto.so",
        "libpassguard.so"
    ];
    
    libraries.forEach(function(libName) {
        try {
            var lib = Module.findBaseAddress(libName);
            if (lib) {
                console.log("✅ " + libName + " - 已加载，基址: " + lib);
            } else {
                console.log("❌ " + libName + " - 未加载");
            }
        } catch (e) {
            console.log("❌ " + libName + " - 检测失败: " + e);
        }
    });
    
    // 测试SM4函数
    try {
        var SM4 = Module.findExportByName("libopenssl.so", "SM4_encrypt");
        if (SM4) {
            console.log("✅ SM4_encrypt函数 - 找到，地址: " + SM4);
        } else {
            console.log("❌ SM4_encrypt函数 - 未找到");
        }
    } catch (e) {
        console.log("❌ SM4_encrypt函数检测失败: " + e);
    }
}

// 测试方法是否存在
function testMethods() {
    Java.perform(function() {
        console.log("🔍 测试关键方法:");
        
        // 测试ClientRpcPack方法
        try {
            var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");
            
            if (ClientRpcPack.encrypt) {
                console.log("✅ ClientRpcPack.encrypt - 存在");
            } else {
                console.log("❌ ClientRpcPack.encrypt - 不存在");
            }
            
            if (ClientRpcPack.decrypt) {
                console.log("✅ ClientRpcPack.decrypt - 存在");
            } else {
                console.log("❌ ClientRpcPack.decrypt - 不存在");
            }
        } catch (e) {
            console.log("❌ ClientRpcPack方法测试失败: " + e);
        }
        
        // 测试Utils方法
        try {
            var Utils = Java.use("com.czcb.mbank.base.utils.Utils");
            
            if (Utils.isRoot) {
                console.log("✅ Utils.isRoot - 存在");
            } else {
                console.log("❌ Utils.isRoot - 不存在");
            }
        } catch (e) {
            console.log("❌ Utils方法测试失败: " + e);
        }
        
        // 测试PassGuard方法
        try {
            var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");
            
            if (PassGuardEdit.getSM2SM4Ciphertext) {
                console.log("✅ PassGuardEdit.getSM2SM4Ciphertext - 存在");
            } else {
                console.log("❌ PassGuardEdit.getSM2SM4Ciphertext - 不存在");
            }
        } catch (e) {
            console.log("❌ PassGuardEdit方法测试失败: " + e);
        }
    });
}

// 列出所有已加载的类
function listLoadedClasses() {
    Java.perform(function() {
        console.log("🔍 搜索相关类:");
        
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                if (className.indexOf("czcb") !== -1 || 
                    className.indexOf("alipay") !== -1 ||
                    className.indexOf("passguard") !== -1) {
                    console.log("📦 找到相关类: " + className);
                }
            },
            onComplete: function() {
                console.log("✅ 类搜索完成");
            }
        });
    });
}

// 主测试函数
function runTests() {
    console.log("🧪 开始Hook环境测试...");
    console.log("=" * 50);
    
    // 基础环境测试
    testJavaEnvironment();
    console.log("");
    
    // 关键类测试
    testKeyClasses();
    console.log("");
    
    // Native库测试
    testNativeLibraries();
    console.log("");
    
    // 方法测试
    testMethods();
    console.log("");
    
    // 延迟执行类搜索，避免输出过多
    setTimeout(function() {
        listLoadedClasses();
    }, 2000);
    
    console.log("🧪 测试完成");
    console.log("=" * 50);
}

// 立即执行测试
setImmediate(runTests);
