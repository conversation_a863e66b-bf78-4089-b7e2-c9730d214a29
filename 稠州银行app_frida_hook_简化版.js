/**
 * 稠州银行手机银行APP Frida Hook脚本 (简化版)
 * 专注于最重要的加密通信组件
 * 基于实际工作的稠州银行.js脚本改进
 */

// 工具函数
function bytesToString(arr) {
    var str = '';
    var i;
    arr = new Uint8Array(arr);
    for (i in arr) {
        str += String.fromCharCode(arr[i]);
    }
    return str;
}

function bytes2String2(jbytes) {
    var string = Java.use('java.lang.String');
    return string.$new(jbytes);
}

function bytesToHex(arr) {
    var str = '';
    var k, j;
    for (var i = 0; i < arr.length; i++) {
        k = arr[i];
        j = k;
        if (k < 0) {
            j = k + 256;
        }
        if (j < 16) {
            str += "0";
        }
        str += j.toString(16);
    }
    return str;
}

// 1. Hook 支付宝加密通信组件 (最重要)
function hookClientRpcPack() {
    Java.perform(function() {
        try {
            var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");
            
            // Hook encrypt方法
            ClientRpcPack.encrypt.implementation = function(var1) {
                console.log("🎯 Class: ClientRpcPack");
                console.log("🔧 Method: encrypt");
                console.log("📥 Arguments:");
                console.log("[0]：Request Data");
                try {
                    console.log(bytes2String2(var1));
                } catch (e) {
                    console.log("无法解析请求数据");
                }
                
                var retval = this.encrypt(var1);
                
                console.log("📤 Return value:");
                console.log("Encrypted Hex: " + bytesToHex(retval));
                console.log("=" * 50);
                return retval;
            };
            
            // Hook decrypt方法
            ClientRpcPack.decrypt.implementation = function(var1) {
                console.log("🎯 Class: ClientRpcPack");
                console.log("🔧 Method: decrypt");
                console.log("📥 Arguments:");
                console.log("[0]：Encrypted Data Hex");
                console.log(bytesToHex(var1));
                
                var retval = this.decrypt(var1);
                
                console.log("📤 Return value:");
                console.log("Decrypted Response:");
                try {
                    console.log(bytes2String2(retval));
                } catch (e) {
                    console.log("无法解析响应数据");
                }
                console.log("=" * 50);
                return retval;
            };
            
            console.log("[+] ClientRpcPack Hook 成功");
        } catch (e) {
            console.log("[-] ClientRpcPack Hook 失败: " + e);
        }
    });
}

// 2. Hook Native层SM4加密
function hookNativeSM4() {
    try {
        var SM4 = Module.findExportByName("libopenssl.so", "SM4_encrypt");
        console.log("🔍 查找SM4_encrypt函数: " + SM4);
        
        if (SM4 != null) {
            Interceptor.attach(SM4, {
                onEnter: function(args) {
                    console.log("🎯 Class: libopenssl.so");
                    console.log("🔧 Method: SM4_encrypt");
                    console.log("📥 Arguments:");
                    console.log("[0] Input data:");
                    console.log(hexdump(args[0], {length: 64}));
                    console.log("[1] Key:");
                    console.log(hexdump(args[1], {length: 32}));
                },
                onLeave: function(retval) {
                    console.log("📤 Return value: " + retval);
                    console.log("=" * 50);
                }
            });
            console.log("[+] Native SM4_encrypt Hook 成功");
        } else {
            console.log("[-] 未找到SM4_encrypt函数");
        }
    } catch (e) {
        console.log("[-] Native SM4 Hook 失败: " + e);
    }
}

// 3. Hook Root检测绕过
function hookRootDetection() {
    Java.perform(function() {
        try {
            var Utils = Java.use("com.czcb.mbank.base.utils.Utils");
            
            Utils.isRoot.implementation = function() {
                console.log("🎯 Class: Utils");
                console.log("🔧 Method: isRoot");
                console.log("📥 Arguments: 无参数");
                console.log("📤 Return value: false (已绕过Root检测)");
                console.log("=" * 50);
                return false;
            };
            
            console.log("[+] Root检测绕过 Hook 成功");
        } catch (e) {
            console.log("[-] Root检测绕过 Hook 失败: " + e);
        }
    });
}

// 4. Hook 随机字符串生成
function hookRandomString() {
    Java.perform(function() {
        try {
            var MiscUtils = Java.use("com.alipay.mobile.common.transport.utils.MiscUtils");
            
            MiscUtils.generateRandomStr.implementation = function(var1) {
                var retval = this.generateRandomStr(var1);
                console.log("🎯 Class: MiscUtils");
                console.log("🔧 Method: generateRandomStr");
                console.log("📥 Arguments:");
                console.log("[0]：Length: " + var1);
                console.log("📤 Return value:");
                console.log("Random Key: " + retval);
                console.log("=" * 50);
                return retval;
            };
            
            console.log("[+] MiscUtils Hook 成功");
        } catch (e) {
            console.log("[-] MiscUtils Hook 失败: " + e);
        }
    });
}

// 5. Hook PassGuard安全键盘
function hookPassGuard() {
    Java.perform(function() {
        try {
            var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");
            
            // Hook 获取加密文本
            PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
                var result = this.getSM2SM4Ciphertext();
                console.log("🎯 Class: PassGuardEdit");
                console.log("🔧 Method: getSM2SM4Ciphertext");
                console.log("📥 Arguments: 无参数");
                console.log("📤 Return value:");
                console.log("Encrypted Password: " + result);
                console.log("=" * 50);
                return result;
            };
            
            // Hook 密码复杂度检测
            PassGuardEdit.isSimple.implementation = function() {
                var result = this.isSimple();
                console.log("🎯 Class: PassGuardEdit");
                console.log("🔧 Method: isSimple");
                console.log("📥 Arguments: 无参数");
                console.log("📤 Return value:");
                console.log("Is Simple Password: " + result);
                console.log("=" * 50);
                return result;
            };
            
            console.log("[+] PassGuardEdit Hook 成功");
        } catch (e) {
            console.log("[-] PassGuardEdit Hook 失败: " + e);
        }
    });
}

// 主函数
function main() {
    console.log("🚀 稠州银行APP Frida Hook脚本 (简化版) 开始加载...");
    console.log("📌 专注于最重要的加密通信组件");
    
    // 立即执行Java Hook
    hookClientRpcPack();
    hookRootDetection();
    hookRandomString();
    hookPassGuard();
    
    // 延迟执行Native Hook，确保so库已加载
    setTimeout(function() {
        hookNativeSM4();
    }, 3000);
    
    console.log("✅ Hook脚本加载完成");
    console.log("📊 监控组件:");
    console.log("   - ClientRpcPack (加密通信)");
    console.log("   - Native SM4 (底层加密)");
    console.log("   - Root检测绕过");
    console.log("   - 随机字符串生成");
    console.log("   - PassGuard安全键盘");
    console.log("=" * 60);
}

// 立即执行
setImmediate(main);
