/**
 * 稠州银行手机银行APP Frida Hook脚本 (纯监控版)
 * 只监控不修改，确保不影响安全键盘正常启动
 * 基于实际工作的稠州银行.js脚本改进
 */

// 工具函数
function bytesToString(arr) {
    var str = '';
    var i;
    arr = new Uint8Array(arr);
    for (i in arr) {
        str += String.fromCharCode(arr[i]);
    }
    return str;
}

function bytes2String2(jbytes) {
    var string = Java.use('java.lang.String');
    return string.$new(jbytes);
}

function bytesToHex(arr) {
    var str = '';
    var k, j;
    for (var i = 0; i < arr.length; i++) {
        k = arr[i];
        j = k;
        if (k < 0) {
            j = k + 256;
        }
        if (j < 16) {
            str += "0";
        }
        str += j.toString(16);
    }
    return str;
}

// 1. Hook 支付宝加密通信组件 (纯监控，不修改)
function hookClientRpcPack() {
    Java.perform(function() {
        try {
            var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");

            // Hook encrypt方法 - 纯监控
            ClientRpcPack.encrypt.implementation = function(var1) {
                // 先调用原方法，确保不影响正常流程
                var retval = this.encrypt(var1);

                // 然后进行监控输出
                try {
                    console.log("🎯 Class: ClientRpcPack");
                    console.log("🔧 Method: encrypt");
                    console.log("📥 Arguments:");
                    console.log("[0]：Request Data");
                    console.log(bytes2String2(var1));
                    console.log("📤 Return value:");
                    console.log("Encrypted Hex: " + bytesToHex(retval));
                    console.log("=" * 50);
                } catch (e) {
                    // 静默处理输出错误，不影响业务流程
                }

                return retval;
            };

            // Hook decrypt方法 - 纯监控
            ClientRpcPack.decrypt.implementation = function(var1) {
                // 先调用原方法
                var retval = this.decrypt(var1);

                // 然后进行监控输出
                try {
                    console.log("🎯 Class: ClientRpcPack");
                    console.log("🔧 Method: decrypt");
                    console.log("📥 Arguments:");
                    console.log("[0]：Encrypted Data Hex");
                    console.log(bytesToHex(var1));
                    console.log("📤 Return value:");
                    console.log("Decrypted Response:");
                    console.log(bytes2String2(retval));
                    console.log("=" * 50);
                } catch (e) {
                    // 静默处理输出错误
                }

                return retval;
            };

            console.log("[+] ClientRpcPack Hook 成功");
        } catch (e) {
            console.log("[-] ClientRpcPack Hook 失败: " + e);
        }
    });
}

// 2. Hook Native层SM4加密
function hookNativeSM4() {
    try {
        var SM4 = Module.findExportByName("libopenssl.so", "SM4_encrypt");
        console.log("🔍 查找SM4_encrypt函数: " + SM4);
        
        if (SM4 != null) {
            Interceptor.attach(SM4, {
                onEnter: function(args) {
                    console.log("🎯 Class: libopenssl.so");
                    console.log("🔧 Method: SM4_encrypt");
                    console.log("📥 Arguments:");
                    console.log("[0] Input data:");
                    console.log(hexdump(args[0], {length: 64}));
                    console.log("[1] Key:");
                    console.log(hexdump(args[1], {length: 32}));
                },
                onLeave: function(retval) {
                    console.log("📤 Return value: " + retval);
                    console.log("=" * 50);
                }
            });
            console.log("[+] Native SM4_encrypt Hook 成功");
        } else {
            console.log("[-] 未找到SM4_encrypt函数");
        }
    } catch (e) {
        console.log("[-] Native SM4 Hook 失败: " + e);
    }
}

// 3. Hook Root检测 (纯监控，不修改返回值)
function hookRootDetection() {
    Java.perform(function() {
        try {
            var Utils = Java.use("com.czcb.mbank.base.utils.Utils");

            Utils.isRoot.implementation = function() {
                // 先调用原方法获取真实结果
                var originalResult = this.isRoot();

                // 监控输出
                try {
                    console.log("🎯 Class: Utils");
                    console.log("🔧 Method: isRoot");
                    console.log("📥 Arguments: 无参数");
                    console.log("📤 Return value: " + originalResult + " (原始值，未修改)");
                    console.log("=" * 50);
                } catch (e) {
                    // 静默处理输出错误
                }

                // 返回原始值，不做任何修改
                return originalResult;
            };

            console.log("[+] Root检测监控 Hook 成功");
        } catch (e) {
            console.log("[-] Root检测监控 Hook 失败: " + e);
        }
    });
}

// 4. Hook 随机字符串生成 (纯监控)
function hookRandomString() {
    Java.perform(function() {
        try {
            var MiscUtils = Java.use("com.alipay.mobile.common.transport.utils.MiscUtils");

            MiscUtils.generateRandomStr.implementation = function(var1) {
                // 先调用原方法
                var retval = this.generateRandomStr(var1);

                // 监控输出
                try {
                    console.log("🎯 Class: MiscUtils");
                    console.log("🔧 Method: generateRandomStr");
                    console.log("📥 Arguments:");
                    console.log("[0]：Length: " + var1);
                    console.log("📤 Return value:");
                    console.log("Random Key: " + retval);
                    console.log("=" * 50);
                } catch (e) {
                    // 静默处理输出错误
                }

                return retval;
            };

            console.log("[+] MiscUtils Hook 成功");
        } catch (e) {
            console.log("[-] MiscUtils Hook 失败: " + e);
        }
    });
}

// 5. Hook PassGuard安全键盘 (延迟Hook，避免影响初始化)
function hookPassGuard() {
    // 延迟Hook，等待安全键盘完全初始化
    setTimeout(function() {
        Java.perform(function() {
            try {
                var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");

                // Hook 获取加密文本 - 纯监控
                PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
                    // 先调用原方法
                    var result = this.getSM2SM4Ciphertext();

                    // 监控输出
                    try {
                        console.log("🎯 Class: PassGuardEdit");
                        console.log("🔧 Method: getSM2SM4Ciphertext");
                        console.log("📥 Arguments: 无参数");
                        console.log("📤 Return value:");
                        console.log("Encrypted Password: " + result);
                        console.log("=" * 50);
                    } catch (e) {
                        // 静默处理输出错误
                    }

                    return result;
                };

                // Hook 密码复杂度检测 - 纯监控
                PassGuardEdit.isSimple.implementation = function() {
                    // 先调用原方法
                    var result = this.isSimple();

                    // 监控输出
                    try {
                        console.log("🎯 Class: PassGuardEdit");
                        console.log("🔧 Method: isSimple");
                        console.log("📥 Arguments: 无参数");
                        console.log("📤 Return value:");
                        console.log("Is Simple Password: " + result);
                        console.log("=" * 50);
                    } catch (e) {
                        // 静默处理输出错误
                    }

                    return result;
                };

                console.log("[+] PassGuardEdit Hook 成功 (延迟Hook)");
            } catch (e) {
                console.log("[-] PassGuardEdit Hook 失败: " + e);
            }
        });
    }, 5000); // 延迟5秒Hook，确保安全键盘已完全初始化
}

// 6. Hook SelfEncryptUtils加密实体方法 (纯监控)
function hookSelfEncryptUtils() {
    Java.perform(function() {
        try {
            var SelfEncryptUtils = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.SelfEncryptUtils");

            // Hook getEncryptedEntity方法 - 纯监控
            SelfEncryptUtils.getEncryptedEntity.implementation = function(bArr, clientRpcPack, httpUrlRequest) {
                // 先调用原方法，确保不影响正常流程
                var retval = this.getEncryptedEntity(bArr, clientRpcPack, httpUrlRequest);

                // 然后进行监控输出
                try {
                    console.log("🎯 Class: SelfEncryptUtils");
                    console.log("🔧 Method: getEncryptedEntity");
                    console.log("📥 Arguments:");
                    console.log("[0] bArr (原始数据):");
                    if (bArr) {
                        console.log("  - 数据长度: " + bArr.length + " 字节");
                        console.log("  - 数据内容: " + bytes2String2(bArr.slice(0, Math.min(100, bArr.length))));
                        console.log("  - 十六进制: " + bytesToHex(bArr.slice(0, Math.min(32, bArr.length))));
                    } else {
                        console.log("  - null");
                    }

                    console.log("[1] clientRpcPack: " + clientRpcPack);
                    console.log("[2] httpUrlRequest: " + httpUrlRequest);

                    console.log("📤 Return value:");
                    console.log("  - 返回对象: " + retval);
                    console.log("  - 对象类型: " + retval.getClass().getName());

                    // 尝试获取加密后的数据
                    if (httpUrlRequest && httpUrlRequest.getReqData) {
                        try {
                            var encryptedData = httpUrlRequest.getReqData();
                            if (encryptedData) {
                                console.log("  - 加密后数据长度: " + encryptedData.length + " 字节");
                                console.log("  - 加密后数据 (十六进制): " + bytesToHex(encryptedData.slice(0, Math.min(32, encryptedData.length))));
                            }
                        } catch (e) {
                            console.log("  - 无法获取加密后数据");
                        }
                    }

                    console.log("=" * 50);
                } catch (e) {
                    // 静默处理输出错误
                }

                return retval;
            };

            console.log("[+] SelfEncryptUtils Hook 成功");
        } catch (e) {
            console.log("[-] SelfEncryptUtils Hook 失败: " + e);
        }
    });
}

// 主函数
function main() {
    console.log("🚀 稠州银行APP Frida Hook脚本 (纯监控版) 开始加载...");
    console.log("📌 只监控不修改，确保不影响安全键盘启动");

    // 立即执行核心Hook (不影响业务流程的)
    hookClientRpcPack();
    hookRandomString();
    hookSelfEncryptUtils();

    // 延迟执行可能影响业务的Hook
    setTimeout(function() {
        hookRootDetection();
    }, 2000);

    // 延迟执行Native Hook，确保so库已加载
    setTimeout(function() {
        hookNativeSM4();
    }, 3000);

    // 最后Hook安全键盘，避免影响初始化
    hookPassGuard();

    console.log("✅ Hook脚本加载完成");
    console.log("📊 监控组件 (纯监控模式):");
    console.log("   - ClientRpcPack (加密通信) - 立即Hook");
    console.log("   - 随机字符串生成 - 立即Hook");
    console.log("   - SelfEncryptUtils (加密实体) - 立即Hook");
    console.log("   - Root检测监控 - 延迟2秒Hook");
    console.log("   - Native SM4 (底层加密) - 延迟3秒Hook");
    console.log("   - PassGuard安全键盘 - 延迟5秒Hook");
    console.log("⚠️  所有Hook均为纯监控，不修改任何返回值");
    console.log("=" * 60);
}

// 立即执行
setImmediate(main);
