# 避免影响安全键盘的Hook策略

## 问题分析

原始Hook脚本导致安全键盘无法正常启动的可能原因：

1. **Hook时机过早** - 在安全键盘初始化过程中就开始Hook
2. **修改返回值** - 特别是Root检测的返回值可能影响安全验证
3. **异常处理不当** - Hook过程中的异常可能中断键盘初始化
4. **同步问题** - Hook可能阻塞了关键的初始化线程

## 解决方案

### 1. 纯监控策略

**原则**: 只监控，不修改任何返回值

```javascript
// ❌ 错误做法 - 修改返回值
Utils.isRoot.implementation = function() {
    return false; // 修改了返回值
};

// ✅ 正确做法 - 纯监控
Utils.isRoot.implementation = function() {
    var originalResult = this.isRoot(); // 先调用原方法
    console.log("Root检测结果: " + originalResult); // 监控输出
    return originalResult; // 返回原始值
};
```

### 2. 延迟Hook策略

**原则**: 等待关键组件初始化完成后再Hook

```javascript
// ❌ 错误做法 - 立即Hook
hookPassGuard();

// ✅ 正确做法 - 延迟Hook
setTimeout(function() {
    hookPassGuard();
}, 5000); // 延迟5秒
```

### 3. 分阶段Hook策略

**原则**: 按重要性和影响程度分阶段Hook

```javascript
// 立即Hook - 不影响业务的
hookClientRpcPack();

// 延迟Hook - 可能影响业务的
setTimeout(() => hookRootDetection(), 2000);
setTimeout(() => hookNativeSM4(), 3000);
setTimeout(() => hookPassGuard(), 5000);
```

### 4. 异常处理策略

**原则**: 所有输出操作都要有异常处理

```javascript
// ✅ 正确做法
PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
    var result = this.getSM2SM4Ciphertext();
    
    try {
        console.log("加密结果: " + result);
    } catch (e) {
        // 静默处理输出错误，不影响业务
    }
    
    return result;
};
```

## 推荐的Hook顺序

### 阶段1: 立即Hook (0秒)
- `ClientRpcPack` - 网络通信加密
- `MiscUtils` - 随机字符串生成

### 阶段2: 延迟Hook (2秒)
- `Utils.isRoot` - Root检测监控

### 阶段3: Native Hook (3秒)
- `SM4_encrypt` - Native层加密

### 阶段4: 安全键盘Hook (5-15秒)
- `KeyboardUtils` - 8秒后
- `PassGuardEdit` - 10秒后
- `CFCA` - 12秒后
- `CustomKeyboard` - 15秒后

## 脚本使用建议

### 1. 基础监控 (推荐)
```bash
# 使用纯监控版简化脚本
frida -U -l 稠州银行app_frida_hook_简化版.js com.czcb.mbank
```

### 2. 安全键盘专用监控
```bash
# 如果只关心安全键盘，使用专用脚本
frida -U -l 安全键盘专用监控脚本.js com.czcb.mbank
```

### 3. 分步测试
```bash
# 第一步：测试环境
frida -U -l 测试Hook脚本.js com.czcb.mbank

# 第二步：基础监控
frida -U -l 稠州银行app_frida_hook_简化版.js com.czcb.mbank

# 第三步：如果需要，使用完整版
frida -U -l 稠州银行app_frida_hook.js com.czcb.mbank
```

## 操作流程

### 1. 启动APP
```bash
# 先启动APP，等待完全加载
adb shell am start -n com.czcb.mbank/.MainActivity
```

### 2. 等待初始化
```bash
# 等待3-5秒，确保APP完全启动
sleep 5
```

### 3. 执行Hook
```bash
# 使用纯监控版脚本
frida -U -l 稠州银行app_frida_hook_简化版.js com.czcb.mbank
```

### 4. 测试登录
- 进入登录界面
- 观察安全键盘是否正常启动
- 查看Hook输出

## 故障排除

### 1. 安全键盘仍无法启动
- 尝试使用安全键盘专用脚本
- 增加Hook延迟时间
- 检查是否有其他安全软件干扰

### 2. Hook不到数据
- 检查APP是否完全启动
- 确认类名和方法名正确
- 查看控制台错误信息

### 3. APP崩溃
- 减少Hook的组件数量
- 增加异常处理
- 使用最简化的监控脚本

## 最佳实践

### 1. 渐进式Hook
- 从最少的Hook开始
- 逐步增加监控组件
- 确认每个组件都不影响业务

### 2. 监控优先级
1. **高优先级**: 网络通信加密 (ClientRpcPack)
2. **中优先级**: 随机数生成、Native加密
3. **低优先级**: 安全键盘、Root检测

### 3. 输出控制
- 重要数据详细输出
- 次要数据简化输出
- 错误信息静默处理

## 文件对比

| 脚本文件 | 特点 | 适用场景 |
|---------|------|----------|
| `稠州银行app_frida_hook_简化版.js` | 纯监控，延迟Hook | 日常分析，推荐使用 |
| `安全键盘专用监控脚本.js` | 专注安全键盘，被动监控 | 键盘问题排查 |
| `稠州银行app_frida_hook.js` | 功能完整，监控全面 | 深度分析 |
| `测试Hook脚本.js` | 环境检测 | 故障排除 |

## 总结

通过采用纯监控策略、延迟Hook和分阶段执行，可以有效避免Hook脚本对安全键盘正常启动的影响。关键是要确保：

1. **不修改任何返回值**
2. **延迟Hook关键组件**
3. **完善的异常处理**
4. **分阶段执行策略**

这样既能获得所需的监控数据，又不会干扰APP的正常功能。
