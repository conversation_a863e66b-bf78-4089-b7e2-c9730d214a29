# SelfEncryptUtils Hook功能说明

## 新增Hook功能

已在稠州银行Hook脚本中添加了对 `com.alipay.mobile.common.transport.http.selfencrypt.SelfEncryptUtils.getEncryptedEntity` 方法的Hook监控。

## Hook的方法

```java
public static AbstractHttpEntity getEncryptedEntity(byte[] bArr, ClientRpcPack clientRpcPack, HttpUrlRequest httpUrlRequest)
```

## 监控内容

### 1. 输入参数监控

#### 参数1: `byte[] bArr` (原始数据)
- **数据长度**: 显示原始数据的字节长度
- **数据内容**: 显示前100字节的字符串内容
- **十六进制**: 显示前32字节的十六进制格式

#### 参数2: `ClientRpcPack clientRpcPack` (加密包对象)
- **对象信息**: 显示ClientRpcPack对象的基本信息

#### 参数3: `HttpUrlRequest httpUrlRequest` (HTTP请求对象)
- **对象信息**: 显示HttpUrlRequest对象信息
- **请求URL**: 尝试获取并显示请求的URL地址

### 2. 返回值监控

#### 返回对象信息
- **对象类型**: 显示返回的AbstractHttpEntity的具体类型
- **对象内容**: 显示返回对象的基本信息

#### 加密后数据
- **数据长度**: 显示加密后数据的字节长度
- **十六进制**: 显示加密后数据的前32字节十六进制格式
- **Base64编码**: 显示加密后数据的Base64编码格式（前100字符）

## 输出格式示例

```
🎯 Class: SelfEncryptUtils
🔧 Method: getEncryptedEntity
📥 Arguments:
[0] bArr (原始数据):
  - 数据长度: 256 字节
  - 数据内容: {"transCode":"MB000010","logonName":"test",...}
  - 十六进制: 7b 22 74 72 61 6e 73 43 6f 64 65 22 3a 22 4d 42
[1] clientRpcPack: com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack@********
[2] httpUrlRequest: com.alipay.mobile.common.transport.http.HttpUrlRequest@********
  - URL: https://mbank.czcb.com.cn/api/gateway
📤 Return value:
  - 返回对象: org.apache.http.entity.ByteArrayEntity@abcdef12
  - 对象类型: org.apache.http.entity.ByteArrayEntity
  - 加密后数据长度: 384 字节
  - 加密后数据 (前32字节十六进制): 48 38 07 28 9f 3c 4a 1b 7e 2d 8f 9a 3b 5c 6e 8d
  - 加密后数据 (Base64): SGgHKJ88Sht+LY+aO1xujdKL3mP9qR7sT4vW8nE2hF...
==================================================
```

## 使用方法

### 1. 使用完整版脚本
```bash
frida -U -l 稠州银行app_frida_hook.js com.czcb.mbank
```

### 2. 使用简化版脚本
```bash
frida -U -l 稠州银行app_frida_hook_简化版.js com.czcb.mbank
```

## 触发时机

该Hook会在以下情况下触发：
1. **用户登录时** - 登录请求数据加密
2. **API调用时** - 各种业务接口请求加密
3. **数据传输时** - 敏感数据传输加密

## 分析价值

### 1. 请求数据分析
- 查看原始请求数据的内容和格式
- 了解业务接口的参数结构
- 分析数据传输的时机

### 2. 加密过程分析
- 监控数据加密的完整过程
- 对比加密前后的数据变化
- 分析加密算法的输入输出

### 3. 网络通信分析
- 了解HTTP请求的目标URL
- 分析请求的路由和接口
- 监控网络通信的频率

## 注意事项

### 1. 性能影响
- Hook会在每次网络请求时触发
- 大量的输出可能影响性能
- 建议在测试环境使用

### 2. 数据安全
- 输出的数据可能包含敏感信息
- 注意保护用户隐私
- 仅用于安全研究目的

### 3. 兼容性
- Hook采用纯监控模式，不修改任何返回值
- 确保不影响APP正常功能
- 如有异常会静默处理

## 扩展功能

可以根据需要进一步扩展：

### 1. 数据过滤
```javascript
// 只监控特定接口的数据
if (httpUrlRequest && httpUrlRequest.getUrl().includes("login")) {
    // 只输出登录相关的数据
}
```

### 2. 数据保存
```javascript
// 将监控数据保存到文件
var fs = require('fs');
fs.writeFileSync('/sdcard/hook_data.txt', JSON.stringify(data));
```

### 3. 实时分析
```javascript
// 实时分析加密数据的模式
if (encryptedData.length > 0) {
    analyzeEncryptionPattern(encryptedData);
}
```

## 故障排除

### 1. Hook失败
```
[-] SelfEncryptUtils Hook 失败: ClassNotFoundException
```
**解决方案**: 确认类名正确，检查APP是否使用了该类

### 2. 方法不存在
```
[-] SelfEncryptUtils Hook 失败: NoSuchMethodError
```
**解决方案**: 检查方法签名，可能需要指定具体的重载版本

### 3. 权限问题
**解决方案**: 确保Frida有足够权限访问目标进程

## 总结

新增的SelfEncryptUtils Hook功能可以帮助您：
- 深入了解稠州银行APP的加密通信机制
- 分析网络请求的数据流转过程
- 监控敏感数据的加密处理
- 为安全分析提供详细的数据支持

这个Hook点是网络通信加密的关键节点，能够提供非常有价值的分析数据。
