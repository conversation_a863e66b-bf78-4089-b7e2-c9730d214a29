/**
 * 通用SO Hook脚本
 * 可以Hook指定SO文件的特定偏移地址，监控内存数据变化
 * 支持多种数据类型和显示格式
 */

// 配置区域 - 根据需要修改这些参数
var CONFIG = {
    // SO文件名
    SO_NAME: "libopenssl.so",
    
    // 要监控的偏移地址列表 (十六进制)
    OFFSETS: [
        "0x212FC0",  // 示例偏移1
        "0x212EC0",  // 示例偏移2
    ],
    
    // 监控数据长度 (字节)
    DATA_LENGTH: 256,
    
    // 监控间隔 (毫秒)
    MONITOR_INTERVAL: 1000,
    
    // 是否启用连续监控
    CONTINUOUS_MONITOR: true,
    
    // 输出格式: "hex", "ascii", "both"
    OUTPUT_FORMAT: "both"
};

// 工具函数
function log(message) {
    console.log("[SO-Hook] " + message);
}

function logError(message) {
    console.log("[SO-Hook ERROR] " + message);
}

// 十六进制转换函数
function bytesToHex(buffer, length) {
    var result = "";
    var bytes = new Uint8Array(buffer);
    for (var i = 0; i < Math.min(length, bytes.length); i++) {
        var hex = bytes[i].toString(16).padStart(2, '0');
        result += hex + " ";
        if ((i + 1) % 16 === 0) result += "\n";
    }
    return result;
}

// ASCII转换函数
function bytesToAscii(buffer, length) {
    var result = "";
    var bytes = new Uint8Array(buffer);
    for (var i = 0; i < Math.min(length, bytes.length); i++) {
        var char = bytes[i];
        if (char >= 32 && char <= 126) {
            result += String.fromCharCode(char);
        } else {
            result += ".";
        }
    }
    return result;
}

// 格式化输出内存数据
function formatMemoryData(address, buffer, length) {
    log("=" * 60);
    log("内存地址: " + address);
    log("数据长度: " + length + " 字节");
    log("时间戳: " + new Date().toISOString());
    
    if (CONFIG.OUTPUT_FORMAT === "hex" || CONFIG.OUTPUT_FORMAT === "both") {
        log("十六进制数据:");
        console.log(bytesToHex(buffer, length));
    }
    
    if (CONFIG.OUTPUT_FORMAT === "ascii" || CONFIG.OUTPUT_FORMAT === "both") {
        log("ASCII数据:");
        console.log(bytesToAscii(buffer, length));
    }
    
    if (CONFIG.OUTPUT_FORMAT === "both") {
        log("Hexdump格式:");
        console.log(hexdump(buffer, {
            length: Math.min(length, 256),
            header: true,
            ansi: true
        }));
    }
    
    log("=" * 60);
}

// 读取指定地址的内存数据
function readMemoryAtOffset(baseAddress, offset, length) {
    try {
        var targetAddress = baseAddress.add(offset);
        var buffer = Memory.readByteArray(targetAddress, length);
        return {
            success: true,
            address: targetAddress,
            buffer: buffer
        };
    } catch (e) {
        return {
            success: false,
            error: e.toString()
        };
    }
}

// 监控指定偏移的内存
function monitorOffset(baseAddress, offset) {
    var offsetInt = parseInt(offset, 16);
    var result = readMemoryAtOffset(baseAddress, offsetInt, CONFIG.DATA_LENGTH);
    
    if (result.success) {
        log("监控偏移: " + offset + " (绝对地址: " + result.address + ")");
        formatMemoryData(result.address, result.buffer, CONFIG.DATA_LENGTH);
    } else {
        logError("读取偏移 " + offset + " 失败: " + result.error);
    }
}

// Hook指定函数并监控内存
function hookFunctionAndMonitor(soName, functionName, offsets) {
    try {
        var funcAddress = Module.findExportByName(soName, functionName);
        if (!funcAddress) {
            logError("未找到函数: " + functionName + " in " + soName);
            return false;
        }
        
        log("找到函数: " + functionName + " at " + funcAddress);
        
        Interceptor.attach(funcAddress, {
            onEnter: function(args) {
                log("函数 " + functionName + " 被调用");
                var baseAddress = Module.findBaseAddress(soName);
                
                offsets.forEach(function(offset) {
                    monitorOffset(baseAddress, offset);
                });
            },
            onLeave: function(retval) {
                log("函数 " + functionName + " 执行完成，返回值: " + retval);
            }
        });
        
        return true;
    } catch (e) {
        logError("Hook函数失败: " + e);
        return false;
    }
}

// 连续监控内存
function startContinuousMonitor(soName, offsets) {
    var baseAddress = Module.findBaseAddress(soName);
    if (!baseAddress) {
        logError("未找到SO: " + soName);
        return;
    }
    
    log("开始连续监控 " + soName + " (基址: " + baseAddress + ")");
    
    setInterval(function() {
        offsets.forEach(function(offset) {
            monitorOffset(baseAddress, offset);
        });
    }, CONFIG.MONITOR_INTERVAL);
}

// 一次性读取内存
function readMemoryOnce(soName, offsets) {
    var baseAddress = Module.findBaseAddress(soName);
    if (!baseAddress) {
        logError("未找到SO: " + soName);
        return;
    }
    
    log("读取 " + soName + " 内存 (基址: " + baseAddress + ")");
    
    offsets.forEach(function(offset) {
        monitorOffset(baseAddress, offset);
    });
}

// Hook内存写入操作
function hookMemoryWrite(soName, offsets) {
    var baseAddress = Module.findBaseAddress(soName);
    if (!baseAddress) {
        logError("未找到SO: " + soName);
        return;
    }
    
    offsets.forEach(function(offset) {
        var offsetInt = parseInt(offset, 16);
        var targetAddress = baseAddress.add(offsetInt);
        
        try {
            Memory.protect(targetAddress, CONFIG.DATA_LENGTH, 'rw-');
            
            Interceptor.attach(targetAddress, {
                onEnter: function(args) {
                    log("检测到内存写入: " + targetAddress + " (偏移: " + offset + ")");
                    var result = readMemoryAtOffset(baseAddress, offsetInt, CONFIG.DATA_LENGTH);
                    if (result.success) {
                        formatMemoryData(result.address, result.buffer, CONFIG.DATA_LENGTH);
                    }
                }
            });
            
            log("已设置内存写入监控: " + targetAddress + " (偏移: " + offset + ")");
        } catch (e) {
            logError("设置内存写入监控失败: " + e);
        }
    });
}

// 搜索内存中的特定模式
function searchMemoryPattern(soName, pattern) {
    var baseAddress = Module.findBaseAddress(soName);
    if (!baseAddress) {
        logError("未找到SO: " + soName);
        return;
    }
    
    try {
        var moduleInfo = Process.getModuleByName(soName);
        var results = Memory.scanSync(moduleInfo.base, moduleInfo.size, pattern);
        
        log("在 " + soName + " 中搜索模式: " + pattern);
        log("找到 " + results.length + " 个匹配项:");
        
        results.forEach(function(result, index) {
            var offset = result.address.sub(baseAddress);
            log("匹配 " + (index + 1) + ": 地址=" + result.address + ", 偏移=" + offset);
            
            var buffer = Memory.readByteArray(result.address, 64);
            formatMemoryData(result.address, buffer, 64);
        });
    } catch (e) {
        logError("搜索内存模式失败: " + e);
    }
}

// 主函数
function main() {
    log("通用SO Hook脚本启动");
    log("目标SO: " + CONFIG.SO_NAME);
    log("监控偏移: " + CONFIG.OFFSETS.join(", "));
    
    // 等待SO加载
    setTimeout(function() {
        var baseAddress = Module.findBaseAddress(CONFIG.SO_NAME);
        if (!baseAddress) {
            logError("SO未加载: " + CONFIG.SO_NAME);
            logError("可用的SO列表:");
            Process.enumerateModules().forEach(function(module) {
                if (module.name.includes(".so")) {
                    console.log("  - " + module.name + " (基址: " + module.base + ")");
                }
            });
            return;
        }
        
        log("SO已加载: " + CONFIG.SO_NAME + " (基址: " + baseAddress + ")");
        
        // 立即读取一次
        readMemoryOnce(CONFIG.SO_NAME, CONFIG.OFFSETS);
        
        // 如果启用连续监控
        if (CONFIG.CONTINUOUS_MONITOR) {
            startContinuousMonitor(CONFIG.SO_NAME, CONFIG.OFFSETS);
        }
        
        // 可选：Hook特定函数
        // hookFunctionAndMonitor(CONFIG.SO_NAME, "SM4_encrypt", CONFIG.OFFSETS);
        
        // 可选：监控内存写入
        // hookMemoryWrite(CONFIG.SO_NAME, CONFIG.OFFSETS);
        
        // 可选：搜索内存模式
        // searchMemoryPattern(CONFIG.SO_NAME, "48 89 e5");
        
    }, 2000);
    
    log("脚本初始化完成");
}

// 导出配置修改函数，方便动态调整
global.updateConfig = function(newConfig) {
    Object.assign(CONFIG, newConfig);
    log("配置已更新: " + JSON.stringify(CONFIG, null, 2));
};

global.readOffset = function(offset) {
    readMemoryOnce(CONFIG.SO_NAME, [offset]);
};

global.searchPattern = function(pattern) {
    searchMemoryPattern(CONFIG.SO_NAME, pattern);
};

// 启动脚本
setImmediate(main);
