# 稠州银行APP Frida Hook脚本使用说明

## 1. 环境准备

### 1.1 安装Frida
```bash
# 安装Frida
pip install frida-tools

# 验证安装
frida --version
```

### 1.2 设备准备
- Android设备需要Root权限
- 或者使用已安装Frida Server的模拟器
- 确保设备已连接并可通过ADB访问

### 1.3 Frida Server部署
```bash
# 下载对应架构的frida-server
# 推送到设备
adb push frida-server /data/local/tmp/
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server
adb shell su -c "/data/local/tmp/frida-server &"
```

## 2. 脚本功能说明

### 2.1 监控的关键组件

#### 加密算法组件
- **SM4Utils**: 国密SM4对称加密算法
- **RSACerPlus**: RSA非对称加密算法  
- **AESCoder**: AES对称加密算法
- **MD5Util**: MD5哈希算法

#### 网络通信组件
- **LoginRpcRequestModel**: 登录相关RPC请求构建
- **RpcTaskHelper**: RPC任务处理框架

#### 安全输入组件
- **PassGuardEdit**: PassGuard第三方安全键盘
- **HKELocalSipEditText**: CFCA安全输入组件
- **KeyboardFullSafeWindow**: 自定义安全键盘

#### 其他安全组件
- **CFCAUtil**: CFCA安全工具类
- **WebViewActivity**: WebView安全配置
- **LoginPresenter**: 登录业务逻辑
- **Hsmcli**: HSM硬件安全模块客户端
- **RandomUtil**: 随机数生成工具
- **AppCache**: 应用缓存管理

### 2.2 输出格式说明

脚本采用统一的输出格式：
```
🎯 Class: 类名
🔧 Method: 方法名
📥 Arguments:
[0]：参数1
[1]：参数2
[2]：参数3
📤 Return value:
返回值内容
==================================================
```

## 3. 使用方法

### 3.1 基本使用
```bash
# 确保稠州银行APP正在运行
# 执行Hook脚本
frida -U -l 稠州银行app_frida_hook.js com.czcb.mbank
```

### 3.2 指定进程ID
```bash
# 查找进程ID
frida-ps -U | grep czcb

# 使用进程ID
frida -U -p <PID> -l 稠州银行app_frida_hook.js
```

### 3.3 保存日志
```bash
# 将输出保存到文件
frida -U -l 稠州银行app_frida_hook.js com.czcb.mbank > hook_log.txt 2>&1
```

## 4. 测试场景

### 4.1 登录流程测试
1. 启动Hook脚本
2. 打开稠州银行APP
3. 进行登录操作
4. 观察以下Hook点：
   - 密码输入时的加密过程
   - 登录请求的构建
   - 网络通信的加密传输

### 4.2 密码输入测试
1. 触发密码输入界面
2. 观察安全键盘的调用：
   - PassGuard键盘初始化
   - 密码加密过程
   - 密码复杂度检测

### 4.3 网络通信测试
1. 执行需要网络请求的操作
2. 观察RPC请求的构建和执行
3. 监控数据加密和传输过程

## 5. 注意事项

### 5.1 安全提醒
- 本脚本仅用于安全研究和测试目的
- 请在合法授权的环境下使用
- 不要用于非法用途

### 5.2 技术限制
- 需要Root权限或调试版本APP
- 某些反调试机制可能影响Hook效果
- 加固的APP可能需要额外的绕过技术

### 5.3 故障排除
- 如果Hook失败，检查类名和方法名是否正确
- 确认Frida Server版本与客户端版本匹配
- 检查设备Root权限和SELinux策略

## 6. 扩展说明

### 6.1 添加新的Hook点
```javascript
Java.perform(function() {
    try {
        var TargetClass = Java.use("目标类名");
        
        TargetClass.targetMethod.implementation = function() {
            var result = this.targetMethod.apply(this, arguments);
            formatOutput("目标类名", "targetMethod", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] TargetClass Hook 成功");
    } catch (e) {
        console.log("[-] TargetClass Hook 失败: " + e);
    }
});
```

### 6.2 修改返回值
```javascript
// 示例：修改密码复杂度检测结果
PassGuardEdit.isSimple.implementation = function() {
    var result = this.isSimple();
    formatOutput("PassGuardEdit", "isSimple", [], result);
    // 强制返回false（密码复杂）
    return false;
};
```

### 6.3 参数修改
```javascript
// 示例：修改加密参数
SM4Utils.encrypt.overload('java.lang.String', 'java.lang.String').implementation = function(data, key) {
    // 记录原始参数
    formatOutput("SM4Utils", "encrypt", [data, key], "调用前");
    
    // 可以在这里修改参数
    var modifiedData = data;
    var modifiedKey = key;
    
    var result = this.encrypt(modifiedData, modifiedKey);
    formatOutput("SM4Utils", "encrypt", [modifiedData, modifiedKey], result);
    return result;
};
```

## 7. 常见问题

### Q1: Hook脚本无法加载
A: 检查Frida Server是否正常运行，确认APP包名是否正确

### Q2: 某些方法Hook失败
A: 可能是方法重载问题，需要指定具体的参数类型

### Q3: 输出信息过多
A: 可以注释掉不需要的Hook点，或添加过滤条件

### Q4: APP崩溃
A: 检查Hook的方法是否正确返回，避免修改关键逻辑

## 8. 相关资源

- [Frida官方文档](https://frida.re/docs/)
- [Android逆向工程指南](https://github.com/android-reverse-engineering)
- [JADX反编译工具](https://github.com/skylot/jadx)

## 9. 免责声明

本脚本和文档仅供安全研究和教育目的使用。使用者应确保在合法合规的前提下使用，作者不承担任何法律责任。
