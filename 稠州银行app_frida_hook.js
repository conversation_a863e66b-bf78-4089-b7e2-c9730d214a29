/**
 * 稠州银行手机银行APP Frida Hook脚本 (纯监控版)
 * 只监控不修改，确保不影响安全键盘正常启动
 * 参考支付宝加密通信机制
 */

// 工具函数：字节数组转字符串
function bytesToString(arr) {
    var str = '';
    var i;
    arr = new Uint8Array(arr);
    for (i in arr) {
        str += String.fromCharCode(arr[i]);
    }
    return str;
}

// 工具函数：Java字节数组转字符串
function bytes2String2(jbytes) {
    var string = Java.use('java.lang.String');
    return string.$new(jbytes);
}

// 工具函数：字节数组转十六进制
function bytesToHex(arr) {
    var str = '';
    var k, j;
    for (var i = 0; i < arr.length; i++) {
        k = arr[i];
        j = k;
        if (k < 0) {
            j = k + 256;
        }
        if (j < 16) {
            str += "0";
        }
        str += j.toString(16);
    }
    return str;
}

// 工具函数：格式化输出
function formatOutput(className, methodName, args, retval) {
    console.log("🎯 Class: " + className);
    console.log("🔧 Method: " + methodName);
    console.log("📥 Arguments:");
    if (args && args.length > 0) {
        for (let i = 0; i < args.length; i++) {
            console.log("[" + i + "]：" + args[i]);
        }
    } else {
        console.log("无参数");
    }
    console.log("📤 Return value:");
    console.log(retval || "void");
    console.log("=" * 50);
}

// 1. Hook 支付宝加密通信组件 (参考稠州银行.js)
Java.perform(function() {
    try {
        var ClientRpcPack = Java.use("com.alipay.mobile.common.transport.http.selfencrypt.ClientRpcPack");

        // Hook encrypt方法 - 纯监控
        ClientRpcPack.encrypt.implementation = function(var1) {
            // 先调用原方法，确保不影响正常流程
            var retval = this.encrypt(var1);

            // 然后进行监控输出
            try {
                console.log("🎯 Class: ClientRpcPack");
                console.log("🔧 Method: encrypt");
                console.log("📥 Arguments:");
                console.log("[0]：Request Data");
                console.log(bytes2String2(var1));
                console.log("📤 Return value:");
                console.log("Encrypted Hex: " + bytesToHex(retval));
                console.log("=" * 50);
            } catch (e) {
                // 静默处理输出错误，不影响业务流程
            }

            return retval;
        };

        // Hook decrypt方法 - 纯监控
        ClientRpcPack.decrypt.implementation = function(var1) {
            // 先调用原方法
            var retval = this.decrypt(var1);

            // 然后进行监控输出
            try {
                console.log("🎯 Class: ClientRpcPack");
                console.log("🔧 Method: decrypt");
                console.log("📥 Arguments:");
                console.log("[0]：Encrypted Data Hex");
                console.log(bytesToHex(var1));
                console.log("📤 Return value:");
                console.log("Decrypted Response:");
                console.log(bytes2String2(retval));
                console.log("=" * 50);
            } catch (e) {
                // 静默处理输出错误
            }

            return retval;
        };

        // Hook SM2公钥获取
        ClientRpcPack.b.overload().implementation = function() {
            var retval = this.b();
            console.log("🎯 Class: ClientRpcPack");
            console.log("🔧 Method: b (getSM2PublicKey)");
            console.log("📥 Arguments: 无参数");
            console.log("📤 Return value:");
            console.log("SM2 PublicKey: " + retval);
            console.log("=" * 50);
            return retval;
        };

        console.log("[+] ClientRpcPack Hook 成功");
    } catch (e) {
        console.log("[-] ClientRpcPack Hook 失败: " + e);
    }
});

// 2. Hook RPC签名相关
Java.perform(function() {
    try {
        var RpcInvoker = Java.use("com.alipay.mobile.common.rpc.RpcInvoker");

        // Hook RPC签名方法
        RpcInvoker.a.overload('java.lang.String', '[B', 'java.lang.String', 'com.alipay.mobile.common.rpc.transport.InnerRpcInvokeContext', '[I').implementation = function(var1, var2, var3, var4, var5) {
            console.log("🎯 Class: RpcInvoker");
            console.log("🔧 Method: a (sign)");
            console.log("📥 Arguments:");
            console.log("[0]：" + var1);
            console.log("[1]：Sign Data: " + bytes2String2(var2));
            console.log("[2]：" + var3);
            console.log("[3]：" + var4);
            console.log("[4]：SignType: " + var5);

            var retval = this.a(var1, var2, var3, var4, var5);

            console.log("📤 Return value:");
            console.log(retval);
            console.log("=" * 50);
            return retval;
        };

        console.log("[+] RpcInvoker Hook 成功");
    } catch (e) {
        console.log("[-] RpcInvoker Hook 失败: " + e);
    }
});

// 3. Hook 随机字符串生成
Java.perform(function() {
    try {
        var MiscUtils = Java.use("com.alipay.mobile.common.transport.utils.MiscUtils");

        // Hook 随机字符串生成
        MiscUtils.generateRandomStr.implementation = function(var1) {
            var retval = this.generateRandomStr(var1);
            console.log("🎯 Class: MiscUtils");
            console.log("🔧 Method: generateRandomStr");
            console.log("📥 Arguments:");
            console.log("[0]：Length: " + var1);
            console.log("📤 Return value:");
            console.log("Random Key: " + retval);
            console.log("=" * 50);
            return retval;
        };

        console.log("[+] MiscUtils Hook 成功");
    } catch (e) {
        console.log("[-] MiscUtils Hook 失败: " + e);
    }
});

// 4. Hook Native层SM4加密 (参考稠州银行.js)
function hookNativeSM4() {
    var SM4 = Module.findExportByName("libopenssl.so", "SM4_encrypt");
    console.log("🔍 查找SM4_encrypt函数: " + SM4);

    if (SM4 != null) {
        Interceptor.attach(SM4, {
            onEnter: function(args) {
                console.log("🎯 Class: libopenssl.so");
                console.log("🔧 Method: SM4_encrypt");
                console.log("📥 Arguments:");
                console.log("[0] Input data:");
                console.log(hexdump(args[0]));
                console.log("[1] Key:");
                console.log(hexdump(args[1]));
                console.log("[2] Output buffer:");
                console.log(hexdump(args[2]));
            },
            onLeave: function(retval) {
                console.log("📤 Return value: " + retval);
                console.log("=" * 50);
            }
        });
        console.log("[+] Native SM4_encrypt Hook 成功");
    } else {
        console.log("[-] 未找到SM4_encrypt函数");
    }
}

// 5. Hook Root检测绕过
Java.perform(function() {
    try {
        var Utils = Java.use("com.czcb.mbank.base.utils.Utils");

        // Hook Root检测
        Utils.isRoot.implementation = function() {
            console.log("🎯 Class: Utils");
            console.log("🔧 Method: isRoot");
            console.log("📥 Arguments: 无参数");
            console.log("📤 Return value: false (已绕过Root检测)");
            console.log("=" * 50);
            return false;
        };

        console.log("[+] Root检测绕过 Hook 成功");
    } catch (e) {
        console.log("[-] Root检测绕过 Hook 失败: " + e);
    }
});

// 6. Hook 稠州银行登录相关RPC请求
Java.perform(function() {
    try {
        var LoginRpcRequestModel = Java.use("com.czcb.mbank.login.rpc.LoginRpcRequestModel");

        // Hook 登录请求
        LoginRpcRequestModel.getZX000010Request.implementation = function() {
            var result = this.getZX000010Request.apply(this, arguments);
            formatOutput("LoginRpcRequestModel", "getZX000010Request", Array.prototype.slice.call(arguments), result);
            return result;
        };

        console.log("[+] LoginRpcRequestModel Hook 成功");
    } catch (e) {
        console.log("[-] LoginRpcRequestModel Hook 失败: " + e);
    }
});

// 7. Hook PassGuard安全键盘
Java.perform(function() {
    try {
        var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");

        // Hook 获取加密文本
        PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
            var result = this.getSM2SM4Ciphertext();
            formatOutput("PassGuardEdit", "getSM2SM4Ciphertext", [], result);
            return result;
        };

        // Hook 获取MD5值
        PassGuardEdit.getMD5.implementation = function() {
            var result = this.getMD5();
            formatOutput("PassGuardEdit", "getMD5", [], result);
            return result;
        };

        // Hook 密码复杂度检测
        PassGuardEdit.isSimple.implementation = function() {
            var result = this.isSimple();
            formatOutput("PassGuardEdit", "isSimple", [], result);
            return result;
        };

        console.log("[+] PassGuardEdit Hook 成功");
    } catch (e) {
        console.log("[-] PassGuardEdit Hook 失败: " + e);
    }
});

// 8. Hook 稠州银行特有的加密工具类
Java.perform(function() {
    try {
        // Hook SM4工具类
        var SM4Utils = Java.use("com.czcb.mbank.base.utils.SM4Utils");
        SM4Utils.encrypt.implementation = function() {
            var result = this.encrypt.apply(this, arguments);
            formatOutput("SM4Utils", "encrypt", Array.prototype.slice.call(arguments), result);
            return result;
        };
        console.log("[+] SM4Utils Hook 成功");
    } catch (e) {
        console.log("[-] SM4Utils Hook 失败: " + e);
    }

    try {
        // Hook MD5工具类
        var MD5Util = Java.use("com.czcb.mbank.base.utils.MD5Util");
        MD5Util.getMD5.implementation = function() {
            var result = this.getMD5.apply(this, arguments);
            formatOutput("MD5Util", "getMD5", Array.prototype.slice.call(arguments), result);
            return result;
        };
        console.log("[+] MD5Util Hook 成功");
    } catch (e) {
        console.log("[-] MD5Util Hook 失败: " + e);
    }
});

// 9. Hook 通用加密相关类
Java.perform(function() {
    // Hook 各种可能的加密工具类
    var encryptionClasses = [
        "com.czcb.mbank.base.utils.EncryptUtils",
        "com.czcb.mbank.base.utils.CryptoUtils",
        "com.czcb.mbank.base.utils.SecurityUtils",
        "com.czcb.mbank.common.utils.EncryptUtil"
    ];

    encryptionClasses.forEach(function(className) {
        try {
            var clazz = Java.use(className);
            console.log("[+] 找到加密类: " + className);

            // 尝试Hook常见的加密方法
            var methods = ['encrypt', 'decrypt', 'encode', 'decode'];
            methods.forEach(function(methodName) {
                try {
                    if (clazz[methodName]) {
                        clazz[methodName].implementation = function() {
                            var result = this[methodName].apply(this, arguments);
                            formatOutput(className, methodName, Array.prototype.slice.call(arguments), result);
                            return result;
                        };
                        console.log("[+] Hook " + className + "." + methodName + " 成功");
                    }
                } catch (e) {
                    // 方法不存在，忽略
                }
            });
        } catch (e) {
            // 类不存在，忽略
        }
    });
});

// 10. 主函数 - 启动所有Hook
function main() {
    console.log("🚀 稠州银行APP Frida Hook脚本 (改进版) 开始加载...");

    // 延迟执行Native Hook，确保so库已加载
    setTimeout(function() {
        hookNativeSM4();
    }, 2000);

    console.log("📊 监控以下关键组件:");
    console.log("   - 支付宝加密通信组件 (ClientRpcPack)");
    console.log("   - RPC签名验证 (RpcInvoker)");
    console.log("   - 随机字符串生成 (MiscUtils)");
    console.log("   - Native SM4加密 (libopenssl.so)");
    console.log("   - Root检测绕过");
    console.log("   - 稠州银行登录RPC");
    console.log("   - PassGuard安全键盘");
    console.log("   - 稠州银行加密工具类");
    console.log("   - 通用加密工具类");
    console.log("=" * 60);
}

// 立即执行主函数
setImmediate(main);
