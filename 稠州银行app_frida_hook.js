/**
 * 稠州银行手机银行APP Frida Hook脚本
 * 用于监控关键安全组件的调用情况
 */

// 工具函数：格式化输出
function formatOutput(className, methodName, args, retval) {
    console.log("🎯 Class: " + className);
    console.log("🔧 Method: " + methodName);
    console.log("📥 Arguments:");
    if (args && args.length > 0) {
        for (let i = 0; i < args.length; i++) {
            console.log("[" + i + "]：" + args[i]);
        }
    } else {
        console.log("无参数");
    }
    console.log("📤 Return value:");
    console.log(retval || "void");
    console.log("=" * 50);
}

// 1. Hook SM4加密算法
Java.perform(function() {
    try {
        var SM4Utils = Java.use("com.czcb.mbank.base.utils.SM4Utils");
        
        // Hook encrypt方法
        SM4Utils.encrypt.overload('java.lang.String', 'java.lang.String').implementation = function(data, key) {
            var result = this.encrypt(data, key);
            formatOutput("SM4Utils", "encrypt", [data, key], result);
            return result;
        };
        
        // Hook decrypt方法
        SM4Utils.decrypt.overload('java.lang.String', 'java.lang.String').implementation = function(data, key) {
            var result = this.decrypt(data, key);
            formatOutput("SM4Utils", "decrypt", [data, key], result);
            return result;
        };
        
        console.log("[+] SM4Utils Hook 成功");
    } catch (e) {
        console.log("[-] SM4Utils Hook 失败: " + e);
    }
});

// 2. Hook MD5工具类
Java.perform(function() {
    try {
        var MD5Util = Java.use("com.czcb.mbank.base.utils.MD5Util");
        
        // Hook MD5加密方法
        MD5Util.getMD5.overload('java.lang.String').implementation = function(input) {
            var result = this.getMD5(input);
            formatOutput("MD5Util", "getMD5", [input], result);
            return result;
        };
        
        console.log("[+] MD5Util Hook 成功");
    } catch (e) {
        console.log("[-] MD5Util Hook 失败: " + e);
    }
});

// 3. Hook RSA加密
Java.perform(function() {
    try {
        var RSACerPlus = Java.use("com.czcb.mbank.hce.security.RSACerPlus");
        
        // Hook RSA加密方法
        RSACerPlus.encrypt.implementation = function() {
            var result = this.encrypt.apply(this, arguments);
            formatOutput("RSACerPlus", "encrypt", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        // Hook RSA解密方法
        RSACerPlus.decrypt.implementation = function() {
            var result = this.decrypt.apply(this, arguments);
            formatOutput("RSACerPlus", "decrypt", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] RSACerPlus Hook 成功");
    } catch (e) {
        console.log("[-] RSACerPlus Hook 失败: " + e);
    }
});

// 4. Hook AES加密
Java.perform(function() {
    try {
        var AESCoder = Java.use("com.czcb.mbank.hce.security.AESCoder");
        
        // Hook AES加密方法
        AESCoder.encrypt.implementation = function() {
            var result = this.encrypt.apply(this, arguments);
            formatOutput("AESCoder", "encrypt", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        // Hook AES解密方法
        AESCoder.decrypt.implementation = function() {
            var result = this.decrypt.apply(this, arguments);
            formatOutput("AESCoder", "decrypt", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] AESCoder Hook 成功");
    } catch (e) {
        console.log("[-] AESCoder Hook 失败: " + e);
    }
});

// 5. Hook 登录相关RPC请求
Java.perform(function() {
    try {
        var LoginRpcRequestModel = Java.use("com.czcb.mbank.login.rpc.LoginRpcRequestModel");
        
        // Hook 登录请求
        LoginRpcRequestModel.getZX000010Request.implementation = function() {
            var result = this.getZX000010Request.apply(this, arguments);
            formatOutput("LoginRpcRequestModel", "getZX000010Request", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        // Hook 手机号验证请求
        LoginRpcRequestModel.getZX000012Request.implementation = function() {
            var result = this.getZX000012Request.apply(this, arguments);
            formatOutput("LoginRpcRequestModel", "getZX000012Request", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        // Hook 短信验证码请求
        LoginRpcRequestModel.getZX000013Request.implementation = function() {
            var result = this.getZX000013Request.apply(this, arguments);
            formatOutput("LoginRpcRequestModel", "getZX000013Request", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] LoginRpcRequestModel Hook 成功");
    } catch (e) {
        console.log("[-] LoginRpcRequestModel Hook 失败: " + e);
    }
});

// 6. Hook RPC任务执行
Java.perform(function() {
    try {
        var RpcTaskHelper = Java.use("com.czcb.mbank.base.net.RpcTaskHelper");
        
        // Hook performTask方法
        RpcTaskHelper.performTask.implementation = function() {
            var result = this.performTask.apply(this, arguments);
            formatOutput("RpcTaskHelper", "performTask", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] RpcTaskHelper Hook 成功");
    } catch (e) {
        console.log("[-] RpcTaskHelper Hook 失败: " + e);
    }
});

// 7. Hook PassGuard安全键盘
Java.perform(function() {
    try {
        var PassGuardEdit = Java.use("cn.passguard.PassGuardEdit");
        
        // Hook 获取加密文本
        PassGuardEdit.getSM2SM4Ciphertext.implementation = function() {
            var result = this.getSM2SM4Ciphertext();
            formatOutput("PassGuardEdit", "getSM2SM4Ciphertext", [], result);
            return result;
        };
        
        // Hook 获取MD5值
        PassGuardEdit.getMD5.implementation = function() {
            var result = this.getMD5();
            formatOutput("PassGuardEdit", "getMD5", [], result);
            return result;
        };
        
        // Hook 密码复杂度检测
        PassGuardEdit.isSimple.implementation = function() {
            var result = this.isSimple();
            formatOutput("PassGuardEdit", "isSimple", [], result);
            return result;
        };
        
        console.log("[+] PassGuardEdit Hook 成功");
    } catch (e) {
        console.log("[-] PassGuardEdit Hook 失败: " + e);
    }
});

// 8. Hook CFCA安全输入
Java.perform(function() {
    try {
        var HKELocalSipEditText = Java.use("com.cfca.mobile.sipedit.hke.HKELocalSipEditText");
        
        // Hook 获取加密数据
        HKELocalSipEditText.getEncryptData.implementation = function() {
            var result = this.getEncryptData();
            formatOutput("HKELocalSipEditText", "getEncryptData", [], result);
            return result;
        };
        
        console.log("[+] HKELocalSipEditText Hook 成功");
    } catch (e) {
        console.log("[-] HKELocalSipEditText Hook 失败: " + e);
    }
});

// 9. Hook 自定义安全键盘
Java.perform(function() {
    try {
        var KeyboardFullSafeWindow = Java.use("com.czcb.mbank.common.keyboard.KeyboardFullSafeWindow");
        
        // Hook PIN码处理
        KeyboardFullSafeWindow.getPin.implementation = function() {
            var result = this.getPin.apply(this, arguments);
            formatOutput("KeyboardFullSafeWindow", "getPin", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        // Hook 一次性填充
        KeyboardFullSafeWindow.oneTimePadding.implementation = function() {
            var result = this.oneTimePadding.apply(this, arguments);
            formatOutput("KeyboardFullSafeWindow", "oneTimePadding", Array.prototype.slice.call(arguments), result);
            return result;
        };
        
        console.log("[+] KeyboardFullSafeWindow Hook 成功");
    } catch (e) {
        console.log("[-] KeyboardFullSafeWindow Hook 失败: " + e);
    }
});

// 10. Hook CFCA工具类
Java.perform(function() {
    try {
        var CFCAUtil = Java.use("com.czcb.mbank.common.cfca.util.CFCAUtil");
        
        // Hook getInstance方法
        CFCAUtil.getInstance.implementation = function() {
            var result = this.getInstance();
            formatOutput("CFCAUtil", "getInstance", [], result);
            return result;
        };
        
        console.log("[+] CFCAUtil Hook 成功");
    } catch (e) {
        console.log("[-] CFCAUtil Hook 失败: " + e);
    }
});

// 11. Hook WebView安全相关
Java.perform(function() {
    try {
        var WebViewActivity = Java.use("com.czcb.mbank.h5service.activity.WebViewActivity");

        // Hook WebView初始化
        WebViewActivity.initView.implementation = function() {
            formatOutput("WebViewActivity", "initView", [], "WebView初始化");
            return this.initView();
        };

        console.log("[+] WebViewActivity Hook 成功");
    } catch (e) {
        console.log("[-] WebViewActivity Hook 失败: " + e);
    }
});

// 12. Hook 登录Presenter
Java.perform(function() {
    try {
        var LoginPresenter = Java.use("com.czcb.mbank.login.presenter.LoginPresenter");

        // Hook 登录方法
        LoginPresenter.login.implementation = function() {
            var result = this.login.apply(this, arguments);
            formatOutput("LoginPresenter", "login", Array.prototype.slice.call(arguments), result);
            return result;
        };

        console.log("[+] LoginPresenter Hook 成功");
    } catch (e) {
        console.log("[-] LoginPresenter Hook 失败: " + e);
    }
});

// 13. Hook 键盘工具类
Java.perform(function() {
    try {
        var KeyboardUtils = Java.use("com.czcb.mbank.common.keyboard.KeyboardUtils");

        // Hook PassGuard初始化
        KeyboardUtils.initPassGuard.implementation = function() {
            var result = this.initPassGuard.apply(this, arguments);
            formatOutput("KeyboardUtils", "initPassGuard", Array.prototype.slice.call(arguments), result);
            return result;
        };

        console.log("[+] KeyboardUtils Hook 成功");
    } catch (e) {
        console.log("[-] KeyboardUtils Hook 失败: " + e);
    }
});

// 14. Hook 随机数工具
Java.perform(function() {
    try {
        var RandomUtil = Java.use("com.czcb.mbank.base.utils.RandomUtil");

        // Hook 获取移动字符串
        RandomUtil.getMoveString.implementation = function() {
            var result = this.getMoveString.apply(this, arguments);
            formatOutput("RandomUtil", "getMoveString", Array.prototype.slice.call(arguments), result);
            return result;
        };

        console.log("[+] RandomUtil Hook 成功");
    } catch (e) {
        console.log("[-] RandomUtil Hook 失败: " + e);
    }
});

// 15. Hook HSM客户端
Java.perform(function() {
    try {
        var Hsmcli = Java.use("com.czcb.mbank.base.utils.Hsmcli");

        // Hook PIN加密
        Hsmcli.pkEncryptAPin.implementation = function() {
            var result = this.pkEncryptAPin.apply(this, arguments);
            formatOutput("Hsmcli", "pkEncryptAPin", Array.prototype.slice.call(arguments), result);
            return result;
        };

        Hsmcli.pkEncryptEPin.implementation = function() {
            var result = this.pkEncryptEPin.apply(this, arguments);
            formatOutput("Hsmcli", "pkEncryptEPin", Array.prototype.slice.call(arguments), result);
            return result;
        };

        // Hook 弱密码检测
        Hsmcli.checkWeakPassword.implementation = function() {
            var result = this.checkWeakPassword.apply(this, arguments);
            formatOutput("Hsmcli", "checkWeakPassword", Array.prototype.slice.call(arguments), result);
            return result;
        };

        console.log("[+] Hsmcli Hook 成功");
    } catch (e) {
        console.log("[-] Hsmcli Hook 失败: " + e);
    }
});

// 16. Hook 应用缓存
Java.perform(function() {
    try {
        var AppCache = Java.use("com.czcb.mbank.base.data.AppCache");

        // Hook 获取实例
        AppCache.getInstance.implementation = function() {
            var result = this.getInstance();
            formatOutput("AppCache", "getInstance", [], "获取AppCache实例");
            return result;
        };

        console.log("[+] AppCache Hook 成功");
    } catch (e) {
        console.log("[-] AppCache Hook 失败: " + e);
    }
});

console.log("🚀 稠州银行APP Frida Hook脚本加载完成");
console.log("📊 监控以下关键组件:");
console.log("   - SM4/AES/RSA加密算法");
console.log("   - MD5哈希算法");
console.log("   - 登录RPC请求");
console.log("   - 网络任务执行");
console.log("   - PassGuard安全键盘");
console.log("   - CFCA安全输入");
console.log("   - 自定义安全键盘");
console.log("   - WebView安全配置");
console.log("   - HSM硬件安全模块");
console.log("   - 随机数生成");
console.log("   - 应用缓存管理");
console.log("=" * 60);
